{"GOLD_M60": [{"timestamp": "2025-09-14 14:52:19", "session_id": "2025-09-14_145219", "avg_accuracy": 0.8593133679858177, "avg_f1_score": 0.5, "avg_auc": 0.8892754386289853, "total_scenarios": 2, "buy_metrics": {"count": 2750, "win_rate": 9.818181818181818, "expectancy": -11.440727272725933, "trade_accuracy": 0.09818181818181819, "trade_f1_score": 0.11781818181818182, "trade_auc": 0.5392727272727272}, "sell_metrics": {"count": 2696, "win_rate": 10.089020771513352, "expectancy": -3.3657270029685304, "trade_accuracy": 0.10089020771513352, "trade_f1_score": 0.12106824925816022, "trade_auc": 0.5403560830860534}}, {"timestamp": "2025-09-14 15:22:28", "session_id": "2025-09-14_152228", "avg_accuracy": 0.9965151240657029, "avg_f1_score": 0.5, "avg_auc": 0.7727490456605766, "total_scenarios": 2, "buy_metrics": {"count": 48, "win_rate": 6.25, "expectancy": -18.37499999999892, "trade_accuracy": 0.0625, "trade_f1_score": 0.075, "trade_auc": 0.525}, "sell_metrics": {"count": 84, "win_rate": 11.904761904761903, "expectancy": -15.000000000001245, "trade_accuracy": 0.11904761904761903, "trade_f1_score": 0.14285714285714282, "trade_auc": 0.5476190476190477}}, {"timestamp": "2025-09-14 15:42:26", "session_id": "2025-09-14_154226", "avg_accuracy": 0.9956708550633149, "avg_f1_score": 0.5, "avg_auc": 0.6913602483338334, "total_scenarios": 2, "buy_metrics": {"count": 1, "win_rate": 0.0, "expectancy": 0.0, "trade_accuracy": 0.0, "trade_f1_score": 0, "trade_auc": 0.5}, "sell_metrics": {"count": 84, "win_rate": 11.904761904761903, "expectancy": -23.130952380953538, "trade_accuracy": 0.11904761904761903, "trade_f1_score": 0.14285714285714282, "trade_auc": 0.5476190476190477}}, {"timestamp": "2025-09-14 16:02:54", "session_id": "2025-09-14_160254", "avg_accuracy": 0.9958474889265498, "avg_f1_score": 0.5, "avg_auc": 0.7326471269908588, "total_scenarios": 2, "buy_metrics": {"count": 1, "win_rate": 0.0, "expectancy": 0.0, "trade_accuracy": 0.0, "trade_f1_score": 0, "trade_auc": 0.5}, "sell_metrics": {"count": 84, "win_rate": 11.904761904761903, "expectancy": -20.892857142858443, "trade_accuracy": 0.11904761904761903, "trade_f1_score": 0.14285714285714282, "trade_auc": 0.5476190476190477}}], "USDJPY_M60": [{"timestamp": "2025-09-14 15:07:03", "session_id": "2025-09-14_150703", "avg_accuracy": 0.8976529771139443, "avg_f1_score": 0.5, "avg_auc": 0.8038316235886284, "total_scenarios": 2, "buy_metrics": {"count": 1733, "win_rate": 9.751875360646277, "expectancy": -8.03635314483513, "trade_accuracy": 0.09751875360646277, "trade_f1_score": 0.11702250432775532, "trade_auc": 0.5390075014425851}, "sell_metrics": {"count": 2100, "win_rate": 8.857142857142856, "expectancy": -10.692857142858598, "trade_accuracy": 0.08857142857142856, "trade_f1_score": 0.10628571428571428, "trade_auc": 0.5354285714285715}}, {"timestamp": "2025-09-14 15:30:32", "session_id": "2025-09-14_153032", "avg_accuracy": 0.9968195373763821, "avg_f1_score": 0.5, "avg_auc": 0.6544201220431728, "total_scenarios": 2, "buy_metrics": {"count": 55, "win_rate": 18.181818181818183, "expectancy": 75.89090909090791, "trade_accuracy": 0.18181818181818182, "trade_f1_score": 0.21818181818181817, "trade_auc": 0.5727272727272728}, "sell_metrics": {"count": 97, "win_rate": 5.154639175257731, "expectancy": -41.422680412372024, "trade_accuracy": 0.05154639175257732, "trade_f1_score": 0.06185567010309278, "trade_auc": 0.520618556701031}}, {"timestamp": "2025-09-14 15:49:10", "session_id": "2025-09-14_154910", "avg_accuracy": 0.9971615462516435, "avg_f1_score": 0.5, "avg_auc": 0.583230976695634, "total_scenarios": 2, "buy_metrics": {"count": 1, "win_rate": 0.0, "expectancy": 0.0, "trade_accuracy": 0.0, "trade_f1_score": 0, "trade_auc": 0.5}, "sell_metrics": {"count": 105, "win_rate": 5.714285714285714, "expectancy": -31.29523809523892, "trade_accuracy": 0.05714285714285714, "trade_f1_score": 0.06857142857142856, "trade_auc": 0.5228571428571429}}, {"timestamp": "2025-09-14 16:09:24", "session_id": "2025-09-14_160924", "avg_accuracy": 0.9968596138844937, "avg_f1_score": 0.5, "avg_auc": 0.5751780971372992, "total_scenarios": 2, "buy_metrics": {"count": 1, "win_rate": 0.0, "expectancy": 0.0, "trade_accuracy": 0.0, "trade_f1_score": 0, "trade_auc": 0.5}, "sell_metrics": {"count": 105, "win_rate": 5.714285714285714, "expectancy": -31.29523809523892, "trade_accuracy": 0.05714285714285714, "trade_f1_score": 0.06857142857142856, "trade_auc": 0.5228571428571429}}]}