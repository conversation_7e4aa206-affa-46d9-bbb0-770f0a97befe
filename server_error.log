
[2025-09-04 11:52:58.203411] ERROR in GOLD M60:
Exception: window must be an integer 0 or greater
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1376, in process_data_and_trade
    df_ft['High_Prev_Max'] = df_ft['High'].rolling(window=nBars_SL).max()
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 12580, in rolling
    return Rolling(
           ^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\window\rolling.py", line 170, in __init__
    self._validate()
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\window\rolling.py", line 1914, in _validate
    raise ValueError("window must be an integer 0 or greater")
ValueError: window must be an integer 0 or greater

==================================================

[2025-09-04 11:54:52.554154] ERROR in GOLD M60:
Exception: "None of [Index(['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100',\n       'EMA200'],\n      dtype='object')] are in the [columns]"
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1542, in process_data_and_trade
    temp_df_for_lag = pd.concat([df_ft[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                 ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4108, in __getitem__
    indexer = self.columns._get_indexer_strict(key, "columns")[1]
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 6200, in _get_indexer_strict
    self._raise_if_missing(keyarr, indexer, axis_name)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 6249, in _raise_if_missing
    raise KeyError(f"None of [{key}] are in the [{axis_name}]")
KeyError: "None of [Index(['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100',\n       'EMA200'],\n      dtype='object')] are in the [columns]"

==================================================

[2025-09-04 11:58:10.258070] ERROR in GOLD M60:
Exception: "None of [Index(['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100',\n       'EMA200'],\n      dtype='object')] are in the [columns]"
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1542, in process_data_and_trade
    temp_df_for_lag = pd.concat([df_ft[['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100', 'EMA200']],
                                 ~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4108, in __getitem__
    indexer = self.columns._get_indexer_strict(key, "columns")[1]
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 6200, in _get_indexer_strict
    self._raise_if_missing(keyarr, indexer, axis_name)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 6249, in _raise_if_missing
    raise KeyError(f"None of [{key}] are in the [{axis_name}]")
KeyError: "None of [Index(['Close', 'Open', 'High', 'Low', 'Volume', 'RSI14', 'EMA50', 'EMA100',\n       'EMA200'],\n      dtype='object')] are in the [columns]"

==================================================

[2025-09-04 12:01:09.136563] ERROR in GOLD M60:
Exception: cannot access local variable 'indicator_features' where it is not associated with a value
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1603, in process_data_and_trade
    if indicator_features.columns.duplicated().any():
       ^^^^^^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'indicator_features' where it is not associated with a value

==================================================

[2025-09-04 13:52:26.890373] ERROR in GOLD M60:
Exception: Cannot save file into a non-existent directory: 'LightGBM\Data_Server'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1427, in process_data_and_trade
    df_ft.to_csv(new_file_path, index=False)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 3967, in to_csv
    return DataFrameRenderer(formatter).to_csv(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\format.py", line 1014, in to_csv
    csv_formatter.save()
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\csvs.py", line 251, in save
    with get_handle(
         ^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\common.py", line 749, in get_handle
    check_parent_directory(str(handle))
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\common.py", line 616, in check_parent_directory
    raise OSError(rf"Cannot save file into a non-existent directory: '{parent}'")
OSError: Cannot save file into a non-existent directory: 'LightGBM\Data_Server'

==================================================

[2025-09-05 10:43:50.802035] ERROR in GOLD M60:
Exception: 'DateTime'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DateTime'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 808, in process_data_and_trade
    df_ft['DateTime'] = df_ft['DateTime'].dt.tz_localize(None)
                        ~~~~~^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DateTime'

==================================================

[2025-09-05 10:49:31.799686] ERROR in GOLD M60:
Exception: Only valid with DatetimeIndex, TimedeltaIndex or PeriodIndex, but got an instance of 'Index'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1559, in process_data_and_trade
    df_d1 = create_resampled_df(symbol, timeframe, base_df, rule='1D', prefix='D1')
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 4832, in create_resampled_df
    resampled_df[f'{prefix}_Open'] = df['Open'].resample(rule).first()
                                     ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 9771, in resample
    return get_resampler(
           ^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\resample.py", line 2050, in get_resampler
    return tg._get_resampler(obj, kind=kind)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\resample.py", line 2272, in _get_resampler
    raise TypeError(
TypeError: Only valid with DatetimeIndex, TimedeltaIndex or PeriodIndex, but got an instance of 'Index'

==================================================

[2025-09-05 10:54:13.049656] ERROR in GOLD M60:
Exception: name 'create_features' is not defined
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 833, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^
NameError: name 'create_features' is not defined. Did you mean: 'create_features_mtf'?

==================================================

[2025-09-05 10:55:49.652885] ERROR in GOLD M60:
Exception: Can only use .dt accessor with datetimelike values
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 834, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 4853, in create_features
    df['DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 6299, in __getattr__
    return object.__getattribute__(self, name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\accessor.py", line 224, in __get__
    accessor_obj = self._accessor(obj)
                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\accessors.py", line 643, in __new__
    raise AttributeError("Can only use .dt accessor with datetimelike values")
AttributeError: Can only use .dt accessor with datetimelike values

==================================================

[2025-09-05 11:00:00.781279] ERROR in GOLD M60:
Exception: Can only use .dt accessor with datetimelike values
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 834, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 4853, in create_features
    df['DayOfWeek'] = df['DateTime'].dt.dayofweek  # 0=Monday, 6=Sunday
                      ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 6299, in __getattr__
    return object.__getattribute__(self, name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\accessor.py", line 224, in __get__
    accessor_obj = self._accessor(obj)
                   ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\accessors.py", line 643, in __new__
    raise AttributeError("Can only use .dt accessor with datetimelike values")
AttributeError: Can only use .dt accessor with datetimelike values

==================================================

[2025-09-05 11:26:41.183451] ERROR in GOLD M60:
Exception: '>=' not supported between instances of 'NoneType' and 'int'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 834, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5341, in create_features
    if timeframe_int >= 240:  # สำหรับ timeframe ขนาดใหญ่ (H4, D1)
       ^^^^^^^^^^^^^^^^^^^^
TypeError: '>=' not supported between instances of 'NoneType' and 'int'

==================================================

[2025-09-05 11:28:55.382364] ERROR in GOLD M60:
Exception: cannot access local variable 'timeframe_int' where it is not associated with a value
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 834, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5339, in create_features
    print(f"ตรวจสอบค่า : timeframe {timeframe} timeframe_int {timeframe_int}")
                                                              ^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'timeframe_int' where it is not associated with a value

==================================================

[2025-09-05 11:31:09.179837] ERROR in GOLD M60:
Exception: cannot access local variable 'timeframe_int' where it is not associated with a value
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 836, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5339, in create_features
    print(f"ตรวจสอบค่า : timeframe {timeframe} timeframe_int {timeframe_int}")
                                                              ^^^^^^^^^^^^^
UnboundLocalError: cannot access local variable 'timeframe_int' where it is not associated with a value

==================================================

[2025-09-05 11:40:54.407435] ERROR in GOLD M60:
Exception: Invalid type <class 'NoneType'>. Must be int or float.
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 836, in process_data_and_trade
    df_with_features = create_features(df_ft, symbol, timeframe, num_nBars_SL)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5629, in create_features
    expected_interval = pd.Timedelta(minutes=timeframe_int)  # ใช้ timeframe จากข้อมูล
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "timedeltas.pyx", line 1777, in pandas._libs.tslibs.timedeltas.Timedelta.__new__
  File "timedeltas.pyx", line 951, in pandas._libs.tslibs.timedeltas._to_py_int_float
TypeError: Invalid type <class 'NoneType'>. Must be int or float.

==================================================

[2025-09-05 11:43:12.896675] ERROR in GOLD M60:
Exception: name 'df_ft_combined' is not defined
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 1560, in process_data_and_trade
    df_ft_combined.to_csv(new_file_path, index=False)
    ^^^^^^^^^^^^^^
NameError: name 'df_ft_combined' is not defined

==================================================

[2025-09-05 11:54:32.818278] ERROR in GOLD M60:
Exception: Cannot save file into a non-existent directory: 'LightGBM\Server_Data'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 827, in process_data_and_trade
    df_ft.to_csv(new_file_path, index=False)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 3967, in to_csv
    return DataFrameRenderer(formatter).to_csv(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\format.py", line 1014, in to_csv
    csv_formatter.save()
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\csvs.py", line 251, in save
    with get_handle(
         ^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\common.py", line 749, in get_handle
    check_parent_directory(str(handle))
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\common.py", line 616, in check_parent_directory
    raise OSError(rf"Cannot save file into a non-existent directory: '{parent}'")
OSError: Cannot save file into a non-existent directory: 'LightGBM\Server_Data'

==================================================

[2025-09-05 12:00:02.029109] ERROR in GOLD M60:
Exception: [Errno 13] Permission denied: 'LightGBM/Data_Trained\\60_GOLD_Data_01b_features.csv'
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 836, in process_data_and_trade
    # ตรวจสอบการโหลดโมเดลตาม Architecture ที่ใช้
        ^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5443, in create_features
    df.to_csv(new_file_path, index=False)
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\util\_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\generic.py", line 3967, in to_csv
    return DataFrameRenderer(formatter).to_csv(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\format.py", line 1014, in to_csv
    csv_formatter.save()
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\formats\csvs.py", line 251, in save
    with get_handle(
         ^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\io\common.py", line 873, in get_handle
    handle = open(
             ^^^^^
PermissionError: [Errno 13] Permission denied: 'LightGBM/Data_Trained\\60_GOLD_Data_01b_features.csv'

==================================================

[2025-09-05 18:35:02.183217] ERROR in GOLD M60:
Exception: cannot access local variable 'df_merged' where it is not associated with a value
Traceback:
Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 832, in process_data_and_trade
    df_ft = create_features(df_ft, symbol, timeframe, num_nBars_SL)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5565, in create_features
    print("Last row before merge (df_merged) :", df_merged.tail(1))
                                                 ^^^^^^^^^
UnboundLocalError: cannot access local variable 'df_merged' where it is not associated with a value

==================================================

[2025-09-05 20:24:02.099601] ERROR in GOLD M60:
Exception: 'DayOfWeek'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DayOfWeek'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 832, in process_data_and_trade
    df_ft = create_features(df_ft, symbol, timeframe, num_nBars_SL)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5738, in create_features
    print(f"\n🔍 Unique values in df['DayOfWeek']: {df['DayOfWeek'].unique()}")
                                                    ~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DayOfWeek'

==================================================

[2025-09-05 20:40:36.022202] ERROR in GOLD M60:
Exception: 'DayOfWeek'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DayOfWeek'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 832, in process_data_and_trade
    df_ft = create_features(df_ft, symbol, timeframe, num_nBars_SL)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5737, in create_features
    print(f"\n🔍 Unique values in df['DayOfWeek']: {df['DayOfWeek'].unique()}")
                                                    ~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DayOfWeek'

==================================================

[2025-09-05 21:00:03.040660] ERROR in GOLD M60:
Exception: 'DayOfWeek'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DayOfWeek'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 832, in process_data_and_trade
    df_ft = create_features(df_ft, symbol, timeframe, num_nBars_SL)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\test_gold\LightGBM_06_folder.py", line 5737, in create_features
    print(f"\n🔍 Unique values in df['DayOfWeek']: {df['DayOfWeek'].unique()}")
                                                    ~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DayOfWeek'

==================================================

[2025-09-06 12:40:57.724166] ERROR in GOLD M60:
Exception: 'DateTime'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DateTime'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 816, in process_data_and_trade
    df_ft['DateTime'] = pd.to_datetime(df['DateTime'])
                                       ~~^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DateTime'

==================================================

[2025-09-06 12:41:28.681102] ERROR in GOLD M60:
Exception: 'DateTime'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DateTime'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 816, in process_data_and_trade
    df_ft['DateTime'] = pd.to_datetime(df['DateTime'])
                                       ~~^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DateTime'

==================================================

[2025-09-06 12:45:59.961588] ERROR in GOLD M60:
Exception: 'DateTime'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DateTime'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 816, in process_data_and_trade
    df_ft['DateTime'] = pd.to_datetime(df['DateTime'])
                                       ~~^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DateTime'

==================================================

[2025-09-06 12:47:57.240668] ERROR in GOLD M60:
Exception: 'DateTime'
Traceback:
Traceback (most recent call last):
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'DateTime'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "d:\test_gold\WebRequest_Server_02_MTF.py", line 816, in process_data_and_trade
    df_ft['DateTime'] = pd.to_datetime(df['DateTime'])
                                       ~~^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\frame.py", line 4102, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'DateTime'

==================================================
