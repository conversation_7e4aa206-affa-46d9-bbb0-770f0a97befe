{"analysis_date": "2025-09-15T07:06:53.704889", "total_models_analyzed": 2, "current_thresholds": {"min_accuracy": 0.5, "min_auc": 0.5, "min_f1": 0.1, "min_precision": 0.1, "min_recall": 0.5, "min_win_rate": 0.3, "min_expectancy": 25.0, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "recommended_thresholds": {"accuracy": {"threshold": 0.9988209544539256, "mean": 0.9990772174991982, "std": 0.0005125260905453621, "min": 0.9985646914086529, "max": 0.9995897435897436, "count": 2}, "auc": {"threshold": 0.8811920236103657, "mean": 0.9189481497848342, "std": 0.07551225234893683, "min": 0.8434358974358973, "max": 0.994460402133771, "count": 2}, "win_rate": {"threshold": 0.65, "mean": 0.65, "std": 0.0, "min": 0.65, "max": 0.65, "count": 2}, "expectancy": {"threshold": 49.97230995266721, "mean": 49.97333848541034, "std": 0.0020570654862588356, "min": 49.97128141992408, "max": 49.9753955508966, "count": 2}}, "new_thresholds": {"min_accuracy": 0.9988209544539256, "min_auc": 0.8811920236103657, "min_f1": 0.1, "min_precision": 0.1, "min_recall": 0.5, "min_win_rate": 0.006500000000000001, "min_expectancy": 49.97230995266721, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.9985646914086529, "auc": 0.8434358974358973, "f1": 0.0, "precision": 0.0, "recall": 0.0}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.9753955508966, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9995897435897436, "auc": 0.994460402133771, "f1": 0.0, "precision": 0.0, "recall": 0.0}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.97128141992408, "num_trades": 0}]}}}