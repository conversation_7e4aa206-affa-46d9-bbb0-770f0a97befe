{"analysis_date": "2025-09-14T16:09:24.664637", "total_models_analyzed": 5, "current_thresholds": {"min_accuracy": 0.5, "min_auc": 0.5, "min_f1": 0.1, "min_precision": 0.1, "min_recall": 0.5, "min_win_rate": 0.3, "min_expectancy": 25.0, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "recommended_thresholds": {"accuracy": {"threshold": 0.9942467827403482, "mean": 0.9969574542505093, "std": 0.0026044668467904777, "min": 0.9936594202898551, "max": 0.9998547250671896, "count": 5}, "auc": {"threshold": 0.8681671692111523, "mean": 0.8979622053992645, "std": 0.06876042063026397, "min": 0.8004050575408843, "max": 0.9942467827403482, "count": 4}, "f1": {"threshold": 0.9941250356130973, "mean": 0.9949271102728456, "std": 0.002174576870043282, "min": 0.9918527667984189, "max": 0.9965312595923421, "count": 3}, "precision": {"threshold": 0.992867076090757, "mean": 0.9948943691562201, "std": 0.0036742915280354132, "min": 0.9900526710329047, "max": 0.9989489552871464, "count": 3}, "recall": {"threshold": 0.9939531015151016, "mean": 0.9951036824402869, "std": 0.0016447407208560073, "min": 0.9936594202898551, "max": 0.9974048442906575, "count": 3}, "win_rate": {"threshold": 0.65, "mean": 0.65, "std": 0.0, "min": 0.65, "max": 0.65, "count": 5}, "expectancy": {"threshold": 49.71705715430761, "mean": 49.79247610631457, "std": 0.1620281842277561, "min": 49.56532860786523, "max": 49.98401936160387, "count": 5}}, "new_thresholds": {"min_accuracy": 0.9942467827403482, "min_auc": 0.8681671692111523, "min_f1": 0.9941250356130973, "min_precision": 0.992867076090757, "min_recall": 0.9939531015151016, "min_win_rate": 0.006500000000000001, "min_expectancy": 49.71705715430761, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.9942467827403482, "auc": 0.9942467827403482, "f1": 0.9965312595923421, "precision": 0.9989489552871464, "recall": 0.9942467827403482}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.71705715430761, "num_trades": 0}]}, "GOLD_M60_counter_trend": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.9974048442906575, "auc": 0.9064424415479171, "f1": 0.9963973044277757, "precision": 0.9956814811486091, "recall": 0.9974048442906575}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.72322840671394, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9996214988644966, "auc": 0.8004050575408843, "f1": 0.0, "precision": 0.0, "recall": 0.0}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.97274700108213, "num_trades": 0}]}, "USDJPY_M60_counter_trend": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.9936594202898551, "auc": 0.8907545397679084, "f1": 0.9918527667984189, "precision": 0.9900526710329047, "recall": 0.9936594202898551}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.56532860786523, "num_trades": 0}]}, "USDJPY_M60_trend_following_Sell": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9998547250671896, "auc": 0.608818828998983, "f1": 0.0, "precision": 0.0, "recall": 0.0}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.98401936160387, "num_trades": 0}]}}}