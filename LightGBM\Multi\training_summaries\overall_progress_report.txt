🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 2
   คะแนนเฉลี่ย: 39.33/100
   Win Rate เฉลี่ย (Test): 66.66%
   Expectancy เฉลี่ย (Test): 5.71

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. USDJPY M060: Score 78.7, Test W% 68.0%, Test Exp 6.13
   2. GOLD M060: Score 0.0, Test W% 65.4%, Test Exp 5.29

⚠️ โมเดลที่ต้องปรับปรุง (Score < 40):
   • GOLD M060: Score 0.0

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 2 โมเดล, Score 39.3, W% 66.7%, Exp 5.71

💰 เปรียบเทียบตาม Symbol:
   GOLD: 1 timeframes, Score 0.0, W% 65.4%
   USDJPY: 1 timeframes, Score 78.7, W% 68.0%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 42.6 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/2 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ❌ ระบบต้องปรับปรุงอย่างมาก
   🔧 มีโมเดลแย่เกิน 30% - ควรทบทวน strategy

📅 อัปเดตล่าสุด: 2025-09-15 07:06:48
================================================================================