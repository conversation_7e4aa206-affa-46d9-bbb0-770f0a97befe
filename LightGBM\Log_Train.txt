
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
✅ Created: LightGBM/Data_Trained (Data Storage)
✅ Created: LightGBM/Hyper_Multi (Hyperparameters)
✅ Created: LightGBM/Multi_Time (Time Used Folder)
✅ Created: LightGBM/Multi (Main Multi-Model)
✅ Created: LightGBM/Multi/feature_importance (Feature Importance)
✅ Created: LightGBM/Multi/individual_performance (Performance Analysis)
✅ Created: LightGBM/Multi/models (Models Base)
✅ Created: LightGBM/Multi/results (Results)
✅ Created: LightGBM/Multi/thresholds (Thresholds)
✅ Created: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-14 20:21:05
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
📁 จำนวนไฟล์ทั้งหมด: 2
   - M60: 2 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 20:21:05
📊 ประมวลผลกลุ่ม M60 (2 ไฟล์)

🏗️ เปิดใช้งาน main
============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED_ZTest.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
🏗️ เปิดใช้งาน get default threshold by scenario
📊 Default threshold for GOLD trend_following: 0.3000
⚠️ ไม่พบไฟล์ threshold LightGBM/Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl สำหรับ trend_following, ใช้ค่า default: 0.29999000000000003 ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl')
threshold (backward compatibility) : threshold 0.29999000000000003 default 0.3

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ trend_following, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GOLD_trend_following_optimal_nBars_SL.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ counter_trend, ใช้ค่า default: None ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GOLD_counter_trend_optimal_nBars_SL.pkl')
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ GOLD MM60, ใช้ค่า default: 4

✅ ข้อมูลที่ส่งเข้า create_features : nBars_SL 4

🏗️ เปิดใช้งาน load and clean data
--- Loading and Cleaning GOLD_H1_FIXED_ZTest.csv ---
✅ อ่านไฟล์สำเร็จด้วย : CSV_Files_Fixed/GOLD_H1_FIXED_ZTest.csv
🔍 ตรวจสอบโครงสร้างไฟล์: CSV_Files_Fixed/GOLD_H1_FIXED_ZTest.csv
📊 จำนวนคอลัมน์: 7
📊 Shape: (26750, 7)
📊 คอลัมน์ปัจจุบัน: ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume']

✅ ข้อมูลที่ส่งเข้า create_features : nBars_SL 4

🏗️ เปิดใช้งาน create features
ตรวจสอบค่า : timeframe M60 timeframe_int 60
🔍 ตรวจสอบ columns ข้อมูล df ก่อนเข้า combined : จำนวน 143
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_OP5', 'EMA_OP10', 'EMA_OP15', 'EMA_CL5', 'EMA_CL10', 'EMA_CL15', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_EMA4', 'RSI_EMA8', 'RSI_EMA12', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'EMA12', 'EMA26', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'Support_100', 'Resistance_100', 'PullBack_100_Up', 'PullBack_100_Down', 'SL_Buy', 'SL_Sell', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']
🔍 ตรวจสอบ columns ข้อมูล df หลัง combined : จำนวน 242
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

--- Merging MTF features ---
🔍 ตรวจสอบ columns ข้อมูล base_df ก่อนเข้า MTF features : จำนวน 241
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create resampled df
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf
🏗️ เปิดใช้งาน create features mtf

Last base_df: 2025-07-11 23:00:00
Last H2: 2025-07-11 22:00:00
Last H4: 2025-07-11 20:00:00
Last D1: 2025-07-11 00:00:00

ตรวจสอบว่า row สุดท้ายยังอยู่หลัง merge:

Last row before merge (df_combined) :              Date      Time     Open     High      Low    Close  Volume            DateTime  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
26749  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542 2025-07-11 23:00:00  ...     2.202605       9686.4     3353.907      4.902312       11443.6    3343.9035     11.128408       9830.05

[1 rows x 242 columns]

Last row before merge (base_df) :                            Date      Time     Open     High      Low    Close  Volume  DayOfWeek  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
DateTime                                                                                          ...                                                                                                            
2025-07-11 23:00:00  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542          4  ...     2.202605       9686.4     3353.907      4.902312       11443.6    3343.9035     11.128408       9830.05

[1 rows x 241 columns]

Last row after merge (df_merged) :                            Date      Time     Open     High      Low    Close  Volume  ...  D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
DateTime                                                                               ...                                                                                                                             
2025-07-11 23:00:00  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542  ...               -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0

[1 rows x 331 columns]

✅ Final df_merged columns: 332
Index(['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime',
       'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight',
       'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB'],
      dtype='object')
🔎 Last row:                               26749
Date                     2025.07.11
Time                       23:00:00
Open                        3356.47
High                        3358.82
Low                         3353.52
...                             ...
D1_Volume_Momentum             -1.0
D1_Volume_TrendStrength        -1.0
D1_MACD_line                   -1.0
D1_MACD_deep                   -1.0
D1_MACD_signal                 -1.0

[332 rows x 1 columns]
🔍 ตรวจสอบ columns ข้อมูล df หลัง mtf : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 204 จาก 26750 แถว)

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2021-01-14 21:00:00 ถึง 2025-07-11 23:00:00
- ระยะเวลารวม: 1639 days 02:00:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 01:28:54.970804294
- ช่วงห่างระหว่างบันทึก (สูงสุด): 3 days 03:00:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 01:00:00
- จำนวนช่วงเวลาที่หายไป: 1159 (จากทั้งหมด 26545 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: 1.5274
p-value: 0.9976
Critical Values:
   1%: -3.4306
   5%: -2.8616
   10%: -2.5668

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -81.6623
p-value: 0.0000
Critical Values:
   1%: -3.4306
   5%: -2.8616
   10%: -2.5668

💾 บันทึกรายงาน Temporal Analysis ที่: LightGBM/Multi/results\M60_GOLD_temporal_report.json

📌 จำนวน Missing Values แสดงเฉพาะคอลัมน์ที่มีค่าว่าง และจำนวน > 0:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)

🔍 Unique values in df['DayOfWeek']: [3 4 0 1 2]

🏗️ เปิดใช้งาน check data quality
==================================================
Data Quality Check for CSV_Files_Fixed/GOLD_H1_FIXED_ZTest.csv
==================================================

[4] Duplicate Rows: 0
💾 บันทึก df_with_features ลงไฟล์ LightGBM/Data_Trained/M60_GOLD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 4 confidence 0.29999000000000003

🏗️ เปิดใช้งาน load and process data

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
⚠️ ไม่พบไฟล์รายชื่อ Features ที่ Model ใช้: LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GOLD MM60
❌ trend_following: ไม่พบไฟล์โมเดล
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
❌ counter_trend: ไม่พบไฟล์โมเดล
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 0/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following', 'trend_following_Buy', 'trend_following_Sell', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
❌ ไม่พบโมเดลใดเลยสำหรับ GOLD MM60
❌ ไม่สามารถโหลดโมเดล Multi-Model ได้ - ใช้ Technical Analysis แทน

🏗️ เปิดใช้งาน try trade with threshold adjustment

🔧 เริ่มทดสอบ Technical Analysis ด้วย reduce_threshold เริ่มต้น 1.0 (threshold จริง: 0.3000)

============================================================
🧪 ครั้งที่ 1: ทดสอบ reduce_threshold = 1.0 (threshold จริง: 0.3000) (Technical Analysis)
============================================================
ใช้ Technical Analysis

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 1.0000

📊 ใช้ Single-Model Architecture
ตรวจสอบการใช้ Model ML : False
model_features: None

❌ ⚠️ ไม่พบ Model ML หรือองค์ประกอบที่จำเป็น จะใช้เงื่อนไขทางเทคนิคแบบดั้งเดิม
🔍 ใช้ start_index = 204 (ข้อมูลทั้งหมด 26546 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 26342
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
✅ พบการเทรด 1833 รายการด้วย reduce_threshold 1.0 (threshold จริง: 0.3000)
📊 ข้อมูลเพียงพอสำหรับ optimization: ดี (train=1099, val=366, test=368)
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.3000 (Technical Analysis)

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 26546 ตัวอย่างข้อมูล df
           Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
204  2021.01.14  21:00:00  1849.67  1851.02  1843.75  1844.32    3646  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
205  2021.01.14  22:00:00  1844.31  1848.30  1844.31  1847.11    3809  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
206  2021.01.14  23:00:00  1847.17  1848.79  1845.67  1845.94     952  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
207  2021.01.15  01:00:00  1846.11  1848.57  1845.28  1846.90    1431  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
208  2021.01.15  02:00:00  1846.82  1852.55  1846.82  1851.71    2008  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 1833 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2021-01-28 14:00:00      1840.65 2021-01-28 15:00:00     1840.65       Sell     0.0          3  ...      3.969286          14.411071       47.967197  0.000000    0.005004               3906.00                      0
1 2021-01-29 05:00:00      1842.19 2021-01-29 07:00:00     1845.57       Sell  -338.0          4  ...      7.485000          24.286745       48.364544  0.001835    0.005504               4702.90                      0
2 2021-02-02 08:00:00      1856.53 2021-02-02 09:00:00     1852.73        Buy  -380.0          1  ...      5.506429          11.760633       46.895872  0.002047    0.006140               4788.25                      0
3 2021-02-03 06:00:00      1841.44 2021-02-03 15:00:00     1841.44       Sell     0.0          2  ...      5.359286          21.904827       45.954245  0.000000    0.005930               4652.40                      0
4 2021-02-05 05:00:00      1794.61 2021-02-05 07:00:00     1797.93       Sell  -332.0          4  ...      6.810714          52.163849       35.197762  0.001850    0.005556               4268.70                      0

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                21.94               
Expectancy          -67.43              
📈 สถิติสำหรับ Sell Trades:
Win%                20.39               
Expectancy          -51.12              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                21.19               
Expectancy          -59.58              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    7.69           390                 
Tuesday   8.36           359                 
Wednesday 9.24           368                 
Thursday  9.72           319                 
Friday    8.82           397                 
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         7.57           185                 
5         13.29          143                 
6         8.15           135                 
7         12.20          123                 
8         8.47           118                 
9         5.04           119                 
10        9.62           104                 
11        5.88           119                 
12        6.32           95                  
13        12.84          109                 
14        10.58          104                 
15        6.12           98                  
16        5.08           59                  
17        10.71          56                  
18        10.64          47                  
19        10.00          60                  
20        5.81           86                  
21        8.22           73                  
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2021-01-28 14:00:00
1   2021-01-29 05:00:00
2   2021-02-02 08:00:00
3   2021-02-03 06:00:00
4   2021-02-05 05:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2021-01-28 13:55:00
1   2021-01-29 04:55:00
2   2021-02-02 07:55:00
3   2021-02-03 05:55:00
4   2021-02-05 04:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 204   2021-01-14 21:00:00
205   2021-01-14 22:00:00
206   2021-01-14 23:00:00
207   2021-01-15 01:00:00
208   2021-01-15 02:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 1833
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2021-01-28 14:00:00      1840.65 2021-01-28 15:00:00     1840.65       Sell     0.0          3          14
1 2021-01-29 05:00:00      1842.19 2021-01-29 07:00:00     1845.57       Sell  -338.0          4           5
2 2021-02-02 08:00:00      1856.53 2021-02-02 09:00:00     1852.73        Buy  -380.0          1           8
3 2021-02-03 06:00:00      1841.44 2021-02-03 15:00:00     1841.44       Sell     0.0          2           6
4 2021-02-05 05:00:00      1794.61 2021-02-05 07:00:00     1797.93       Sell  -332.0          4           5

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 1833/1833 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 74 samples (4.0%)
  Class 1 (weak_sell): 524 samples (28.6%)
  Class 2 (no_trade): 595 samples (32.5%)
  Class 3 (weak_buy): 554 samples (30.2%)
  Class 4 (strong_buy): 86 samples (4.7%)
✅ Multi-class Target ถูกต้อง: 5 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     74
1    524
2    595
3    554
4     86
Name: count, dtype: int64
Class 0 (strong_sell): 74 trades, Profit range: 225.0 to 4975.0
Class 1 (weak_sell): 524 trades, Profit range: 0.0 to 0.0
Class 2 (no_trade): 595 trades, Profit range: -3003.0 to -98.0
Class 3 (weak_buy): 554 trades, Profit range: 0.0 to 0.0
Class 4 (strong_buy): 86 trades, Profit range: 118.0 to 4587.0
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    1674
1     159
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    861
1     85
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    813
1     74
Name: count, dtype: int64
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 1833
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time        EMA50       EMA100       EMA200      RSI14
0 2021-01-28 14:00:00  1844.845180  1848.744999  1854.655823  47.967197
1 2021-01-29 05:00:00  1844.797996  1847.825800  1853.408345  48.364544
2 2021-02-02 08:00:00  1856.620624  1854.195961  1855.056190  46.895872
3 2021-02-03 06:00:00  1847.357469  1849.557966  1852.369436  45.954245
4 2021-02-05 05:00:00  1816.900628  1829.069074  1839.626409  35.197762

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    1674
1     159
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                     1.000000
RSI_ROC_i4                 0.055330
D1_Volume_Spike            0.054780
H4_Bar_DTB                 0.052073
H2_Bar_FVG                 0.050489
                             ...   
RSI_counter                     NaN
RSI_Divergence_i2               NaN
Price_EMA50_x_RSI_trend         NaN
RSI_Divergence_i6               NaN
D1_Bar_DTB                      NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
             feature         VIF
0         RSI_ROC_i4   25.778203
1    D1_Volume_Spike    1.713443
2         H4_Bar_DTB    1.112322
3         H2_Bar_FVG    2.044705
4           BB_width  503.073603
..               ...         ...
202  Volume_Change_5    1.883682
203     H8_Bar_CL_HL    3.742162
204   EMA_diff_x_ATR   47.795270
205      ADX_zone_15    1.495419
206       Dist_EMA50  214.258054

[207 rows x 2 columns]

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['RSI_ROC_i4', 'BB_width', 'RSI14_x_BBwidth', 'Close_Std_20', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'ADX_14_x_BBwidth', 'H12_Price_Range', 'DMP_14_Lag_5', 'DMP_14_Lag_3', 'Close_Return_5', 'BB_width_Lag_5', 'H8_MACD_line', 'MA_Cross_100_200', 'RSI_ROC_i8', 'DMN_14_Lag_5', 'ATR_x_PriceRange', 'MACD_signal_x_ADX', 'ATR_ROC_i4', 'Close_Lag_20', 'Low_Lag_20', 'Open_Lag_15', 'High_Lag_20', 'EMA200_Lag_5', 'EMA200_Lag_3', 'EMA200_Lag_2', 'EMA200_Lag_1', 'Open_Lag_20', 'High_Lag_15', 'Low_Lag_15', 'High_Lag_50', 'Close_Lag_50', 'EMA100_Lag_5', 'MACD_12_26_9_Lag_3', 'EMA100_Lag_3', 'Close_Lag_15', 'EMA100_Lag_2', 'EMA100_Lag_1', 'Low_Lag_50', 'Low_Lag_1', 'EMA50_Lag_5', 'Open_Lag_50', 'EMA50_Lag_3', 'EMA50_Lag_2', 'EMA50_Lag_1', 'Close_Lag_1', 'Close_MA_20', 'High_Lag_1', 'Low_Lag_30', 'Open_Lag_1', 'Close_Lag_2', 'High_Lag_30', 'Close_Lag_30', 'Close_MA_3', 'Open_Lag_30', 'High_Lag_2', 'RSI14_Lag_5', 'Open_Lag_5', 'Low_Lag_2', 'Close_Lag_3', 'Open_Lag_2', 'Close_MA_5', 'High_Lag_5', 'High_Lag_10', 'Low_Lag_5', 'Low_Lag_3', 'Open_Lag_10', 'Close_MA_10', 'High_Lag_3', 'Close_Lag_5', 'Low_Lag_10', 'Close_Lag_10', 'Open_Lag_3', 'STOCHd_14_3_3_Lag_5', 'RSI14_x_StochK', 'MACD_signal_x_RSI14', 'MACD_12_26_9_Lag_5', 'MACD_12_26_9_Lag_2', 'ADX_14_x_ATR', 'ATR_Lag_3', 'DMP_14_Lag_2', 'DMN_14_Lag_3', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'Close_Return_3', 'MACDs_12_26_9_Lag_3', 'STOCHk_14_3_3_Lag_1', 'MACD_12_26_9_Lag_1', 'ATR_Lag_2', 'RSI14_x_ATR', 'ATR_Lag_5', 'EMA_diff_x_BBwidth', 'ATR_Lag_1', 'STOCHd_14_3_3_Lag_3', 'RSI_ROC_i6', 'Close_Return_1', 'ATR_ROC_i2', 'RSI14_x_StochD', 'PullBack_100_Up', 'PullBack_100_Down', 'MACDs_12_26_9_Lag_5', 'DMN_14_Lag_2', 'Price_Move', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'RSI14_x_PriceMove', 'ATR_ROC_i8', 'ATR_ROC_i6', 'Dist_EMA100', 'STOCHd_14_3_3_Lag_2', 'Slope_EMA200', 'Bar_CL', 'DMP_14_Lag_1', 'Momentum5_x_Volatility10', 'Price_Range', 'EMA_diff_x_ATR', 'Dist_EMA50']

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

ℹ️ ⚠️ ไม่พบไฟล์ Features ที่จำเป็น 'LightGBM/Multi\feature_importance\M60_must_have_features.pkl'. จะใช้ Hardcoded must_have_features_in_model เป็นค่าเริ่มต้น

featuresasset_feature_importance : len  6
['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight']
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 91
⚠️ Features มากเกินไป (91 > 50)
✂️ ตัด features ที่มี correlation ต่ำสุด
✅ เหลือ 50 features

✅ Final selected features for training: 50 features
📋 Top 10 features:
1. D1_Volume_Spike (corr: 0.0548)
2. H4_Bar_DTB (corr: 0.0521)
3. H2_Bar_FVG (corr: 0.0505)
4. Bar_TL (corr: 0.0434)
5. RSI_signal_EMA12 (corr: 0.0427)
6. D1_Bar_FVG (corr: 0.0427)
7. H12_Bar_TL (corr: 0.0406)
8. IsMorning (corr: 0.0405)
9. H12_Price_Strangth (corr: 0.0405)
10. H12_Bar_CL_OC (corr: 0.0399)
... และอีก 40 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 50
1. D1_Volume_Spike
2. H4_Bar_DTB
3. H2_Bar_FVG
4. Bar_TL
5. RSI_signal_EMA12
6. D1_Bar_FVG
7. H12_Bar_TL
8. IsMorning
9. H12_Price_Strangth
10. H12_Bar_CL_OC
11. EMA_Cross_EMA10
12. ATR_Deep
13. Bar_OSB
14. STO_zone
15. Volume_Lag_50
... และอีก 35 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.913257
1    0.086743
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.09
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                           count         mean          std          min          25%          50%          75%           max
D1_Volume_Spike           1833.0     0.123841     0.329490     0.000000     0.000000     0.000000     0.000000      1.000000
H4_Bar_DTB                1833.0     0.000546     0.040463    -1.000000     0.000000     0.000000     0.000000      1.000000
H2_Bar_FVG                1833.0    -0.004364     0.357366    -1.000000     0.000000     0.000000     0.000000      1.000000
Bar_TL                    1833.0     0.348063     0.476486     0.000000     0.000000     0.000000     1.000000      1.000000
RSI_signal_EMA12          1833.0    -0.006001     1.000255    -1.000000    -1.000000    -1.000000     1.000000      1.000000
D1_Bar_FVG                1833.0     0.000000     0.363450    -1.000000     0.000000     0.000000     0.000000      1.000000
H12_Bar_TL                1833.0     0.159302     0.366057     0.000000     0.000000     0.000000     0.000000      1.000000
IsMorning                 1833.0     0.238407     0.426226     0.000000     0.000000     0.000000     0.000000      1.000000
H12_Price_Strangth        1833.0    -0.006001     0.576531    -1.000000     0.000000     0.000000     0.000000      1.000000
H12_Bar_CL_OC             1833.0    -0.046918     0.847539    -1.000000    -1.000000     0.000000     1.000000      1.000000
EMA_Cross_EMA10           1833.0     0.010366     1.000219    -1.000000    -1.000000     1.000000     1.000000      1.000000
ATR_Deep                  1833.0    -0.169122     0.985587    -1.000000    -1.000000    -1.000000     1.000000      1.000000
Bar_OSB                   1833.0     0.010366     0.356477    -1.000000     0.000000     0.000000     0.000000      1.000000
STO_zone                  1833.0     0.039825     0.999479    -1.000000    -1.000000     1.000000     1.000000      1.000000
Volume_Lag_50             1833.0  5412.214403  4583.306215   398.000000  2377.000000  4018.000000  7139.000000  50642.000000
H2_Volume_Spike           1833.0     0.213312     0.409758     0.000000     0.000000     0.000000     0.000000      1.000000
H12_Bar_SW                1833.0    -0.012002     0.699242    -1.000000    -1.000000     0.000000     0.000000      1.000000
H8_Volume_TrendStrength   1833.0    -0.032188     0.999755    -1.000000    -1.000000    -1.000000     1.000000      1.000000
MA_Cross_50_200           1833.0     0.044190     0.999296    -1.000000    -1.000000     1.000000     1.000000      1.000000
H8_Price_Strangth         1833.0    -0.064921     0.667097    -1.000000    -1.000000     0.000000     0.000000      1.000000
H12_Price_Move            1833.0    -0.627081    14.430131   -65.730000    -7.310000    -0.330000     6.760000     77.300000
H4_Bar_CL_OC              1833.0     0.031642     0.857845    -1.000000    -1.000000     0.000000     1.000000      1.000000
H8_Volume_Momentum        1833.0    -0.036552     0.999604    -1.000000    -1.000000    -1.000000     1.000000      1.000000
H2_Volume_Momentum        1833.0     0.204583     0.979116    -1.000000    -1.000000     1.000000     1.000000      1.000000
H2_Price_Move             1833.0    -0.115003     5.612807   -52.180000    -2.230000     0.090000     2.170000     29.390000
H12_Bar_FVG               1833.0     0.006001     0.353502    -1.000000     0.000000     0.000000     0.000000      1.000000
H12_Bar_longwick          1833.0     0.045543     1.922118   -28.156250    -0.621222     0.103853     0.728070     12.384058
EMA_Cross_EMA5            1833.0     0.007092     1.000248    -1.000000    -1.000000     1.000000     1.000000      1.000000
D1_Price_Range            1833.0    31.834894    20.299546     7.380000    19.150000    26.230000    38.040000    133.230000
H4_Volume_Momentum        1833.0     0.153301     0.988449    -1.000000    -1.000000     1.000000     1.000000      1.000000
H8_Price_Range            1833.0    16.230125    11.648391     2.870000     8.780000    13.240000    19.550000     97.650000
Bar_CL_HL                 1833.0     0.013093     0.461206    -1.000000     0.000000     0.000000     0.000000      1.000000
H4_Bar_longwick           1833.0    -0.030432     1.638417   -11.081356    -0.671400    -0.135802     0.597938     20.500000
D1_MACD_signal            1833.0    -0.000546     0.994252    -1.000000    -1.000000     0.000000     1.000000      1.000000
H4_Bar_OSB                1833.0     0.008729     0.385221    -1.000000     0.000000     0.000000     0.000000      1.000000
H4_Bar_SW                 1833.0     0.021277     0.714850    -1.000000     0.000000     0.000000     1.000000      1.000000
Close_Std_10              1833.0     3.844398     3.291290     0.450674     1.918260     2.935200     4.495567     29.640733
H2_Price_Strangth         1833.0    -0.022913     0.567995    -1.000000     0.000000     0.000000     0.000000      1.000000
Volume_Momentum           1833.0    -0.079105     0.997138    -1.000000    -1.000000    -1.000000     1.000000      1.000000
H4_Volume_TrendStrength   1833.0    -0.428260     0.903902    -1.000000    -1.000000    -1.000000     1.000000      1.000000
Volume_MA_10              1833.0  5621.342881  3353.022016  1427.000000  3288.200000  4790.000000  7072.000000  30191.200000
H8_Bar_TL                 1833.0     0.177305     0.382031     0.000000     0.000000     0.000000     0.000000      1.000000
Rolling_Close_5           1833.0     0.001364     0.000978     0.000101     0.000730     0.001131     0.001671      0.012200
BB_Break_HL               1833.0    -0.011457     0.422329    -1.000000     0.000000     0.000000     0.000000      1.000000
H8_Volume_Spike           1833.0     0.255865     0.436465     0.000000     0.000000     0.000000     1.000000      1.000000
Hour                      1833.0     9.969995     5.125678     3.000000     5.000000     9.000000    14.000000     20.000000
H12_Volume_TrendStrength  1833.0     0.204583     0.979116    -1.000000    -1.000000     1.000000     1.000000      1.000000
D1_Bar_TL                 1833.0     0.201855     0.401494     0.000000     0.000000     0.000000     0.000000      1.000000
Volume_Lag_20             1833.0  6999.972177  5770.518352   516.000000  3343.000000  5609.000000  8928.000000  71230.000000
H8_Bar_CL_OC              1833.0     0.013093     0.818617    -1.000000    -1.000000     0.000000     1.000000      1.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...
การกระจายของ Target ในชุดข้อมูล:

📊 ใช้ Target Column: Target_Multiclass
Train: Target_Multiclass
2    0.341219
1    0.297543
3    0.273885
0    0.050045
4    0.037307
Name: proportion, dtype: float64
Val: Target_Multiclass
3    0.333333
2    0.314208
1    0.256831
4    0.068306
0    0.027322
Name: proportion, dtype: float64
Test: Target_Multiclass
3    0.355978
2    0.285326
1    0.279891
4    0.054348
0    0.024457
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338

🏗️ เปิดใช้งาน analyze time filters

📊 Time Filter Analysis for GOLD:
📅 Recommended Days: ['Thursday', 'Friday']
⏰ Recommended Hours: [3, 6, 9, 12, 16]
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (      D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG    Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike      Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
0           -0.367873   -0.042698   -0.012982  1.370023          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228  0.611089                 -1.256906  -0.527169       0.170684     -1.231509
1           -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799         1.591780 -1.144949                  0.795605   1.896926      -0.591761     -0.023091
2            2.718329   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -2.357133         1.591780 -0.559603                  0.795605  -0.527169       0.202822     -0.023091
3           -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228 -0.949834                  0.795605  -0.527169      -0.357908     -1.231509
4           -0.367873   -0.042698   -0.012982  1.370023          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228 -1.144949                  0.795605  -0.527169      -0.317210     -1.231509
...               ...         ...         ...       ...               ...         ...         ...  ...          ...              ...       ...                       ...        ...            ...           ...
1094        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799         1.591780 -0.559603                  0.795605  -0.527169       0.115612      1.185327
1095        -0.367873   -0.042698   -0.012982  1.370023         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228  0.025743                  0.795605  -0.527169      -0.065430     -0.023091
1096        -0.367873   -0.042698    2.840457 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799         1.591780  1.391551                 -1.256906  -0.527169       0.061348     -1.231509
1097        -0.367873   -0.042698   -2.866421 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228 -0.754718                  0.795605  -0.527169       0.127886     -0.023091
1098        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799         1.591780 -0.949834                  0.795605  -0.527169      -0.423316     -0.023091

[1099 rows x 50 columns], 0       1
1       2
2       2
3       1
4       2
       ..
1094    2
1095    3
1096    3
1097    4
1098    2
Name: Target_Multiclass, Length: 1099, dtype: int32)
val_data : (      D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG    Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike      Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
1099        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799         1.591780 -0.559603                  0.795605  -0.527169       0.159540     -0.023091
1100        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228  0.220859                  0.795605  -0.527169      -0.046050     -0.023091
1101        -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228  0.611089                 -1.256906  -0.527169       1.261944     -0.023091
1102         2.718329   -0.042698   -2.866421 -0.729915         -0.995461    2.834014    2.431450  ...    -2.357133         1.591780 -1.144949                  0.795605  -0.527169      -0.636173      1.185327
1103         2.718329   -0.042698   -0.012982 -0.729915          1.004560    2.834014    2.431450  ...    -0.012799        -0.628228  0.806205                 -1.256906  -0.527169       1.268727     -1.231509
...               ...         ...         ...       ...               ...         ...         ...  ...          ...              ...       ...                       ...        ...            ...           ...
1460        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -2.357133        -0.628228  0.611089                 -1.256906  -0.527169       1.523575     -0.023091
1461        -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497    2.431450  ...    -0.012799         1.591780 -1.340064                  0.795605  -0.527169      -0.011004     -0.023091
1462        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497    2.431450  ...    -0.012799         1.591780 -0.754718                  0.795605  -0.527169       0.420525     -0.023091
1463        -0.367873   -0.042698   -2.866421 -0.729915         -0.995461    0.065497    2.431450  ...    -0.012799        -0.628228 -0.364488                  0.795605  -0.527169       0.152273     -1.231509
1464        -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228  0.415974                 -1.256906  -0.527169       0.556993     -1.231509

[366 rows x 50 columns], 1099    3
1100    3
1101    3
1102    4
1103    3
       ..
1460    3
1461    2
1462    3
1463    3
1464    2
Name: Target_Multiclass, Length: 366, dtype: int32)
test_data : (      D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG    Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike      Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
1465        -0.367873   -0.042698   -0.012982  1.370023         -0.995461    0.065497   -0.411277  ...    -2.357133        -0.628228  1.001320                 -1.256906  -0.527169       0.912456     -1.231509
1466        -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228 -1.340064                  0.795605  -0.527169       0.098655     -1.231509
1467        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228 -0.364488                  0.795605  -0.527169       0.239645     -0.023091
1468        -0.367873   -0.042698   -2.866421  1.370023         -0.995461    0.065497    2.431450  ...    -2.357133        -0.628228 -0.754718                  0.795605  -0.527169       0.440551     -0.023091
1469        -0.367873   -0.042698   -0.012982  1.370023         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228  0.611089                 -1.256906  -0.527169       1.454452     -1.231509
...               ...         ...         ...       ...               ...         ...         ...  ...          ...              ...       ...                       ...        ...            ...           ...
1828        -0.367873   -0.042698   -0.012982  1.370023          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228  1.976897                 -1.256906  -0.527169      -0.452386     -0.023091
1829        -0.367873   -0.042698   -0.012982 -0.729915          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228 -0.949834                  0.795605   1.896926       0.338806      1.185327
1830        -0.367873   -0.042698   -0.012982  1.370023          1.004560    0.065497   -0.411277  ...    -0.012799        -0.628228 -0.364488                  0.795605   1.896926       0.559416      1.185327
1831        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...    -0.012799        -0.628228  0.025743                  0.795605   1.896926      -0.023278      1.185327
1832        -0.367873   -0.042698   -0.012982 -0.729915         -0.995461    0.065497   -0.411277  ...     2.331535        -0.628228  0.806205                 -1.256906   1.896926       1.301027      1.185327

[368 rows x 50 columns], 1465    3
1466    4
1467    4
1468    3
1469    3
       ..
1828    2
1829    1
1830    2
1831    2
1832    1
Name: Target_Multiclass, Length: 368, dtype: int32)
df :              Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
204    2021.01.14  21:00:00  1849.67  1851.02  1843.75  1844.32    3646  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
205    2021.01.14  22:00:00  1844.31  1848.30  1844.31  1847.11    3809  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
206    2021.01.14  23:00:00  1847.17  1848.79  1845.67  1845.94     952  ...               1.0              0.0                -1.0                      0.0           0.0           0.0             0.0
207    2021.01.15  01:00:00  1846.11  1848.57  1845.28  1846.90    1431  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
208    2021.01.15  02:00:00  1846.82  1852.55  1846.82  1851.71    2008  ...               0.0              0.0                 1.0                      0.0           0.0           0.0             0.0
...           ...       ...      ...      ...      ...      ...     ...  ...               ...              ...                 ...                      ...           ...           ...             ...
26745  2025.07.11  19:00:00  3358.83  3359.63  3352.79  3353.84   11242  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
26746  2025.07.11  20:00:00  3353.65  3354.09  3349.24  3353.38    9305  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
26747  2025.07.11  21:00:00  3353.37  3358.48  3353.32  3356.09    7062  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
26748  2025.07.11  22:00:00  3356.09  3359.72  3355.59  3356.40    7058  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
26749  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0

[26546 rows x 332 columns]
trade_df :               Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  D1_MACD_signal  RR_Ratio  Target Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0    2021-01-28 14:00:00      1840.65 2021-01-28 15:00:00     1840.65       Sell     0.0          3  ...             0.0       inf       0           0          -1            0                  1
1    2021-01-29 05:00:00      1842.19 2021-01-29 07:00:00     1845.57       Sell  -338.0          4  ...             0.0  3.000000       0           0          -1            0                  2
2    2021-02-02 08:00:00      1856.53 2021-02-02 09:00:00     1852.73        Buy  -380.0          1  ...             0.0  3.000000       0           0           0           -1                  2
3    2021-02-03 06:00:00      1841.44 2021-02-03 15:00:00     1841.44       Sell     0.0          2  ...             0.0       inf       0           0          -1            0                  1
4    2021-02-05 05:00:00      1794.61 2021-02-05 07:00:00     1797.93       Sell  -332.0          4  ...             0.0  3.003012       0           0          -1            0                  2
...                  ...          ...                 ...         ...        ...     ...        ...  ...             ...       ...     ...         ...         ...          ...                ...
1828 2025-07-09 21:00:00      3309.53 2025-07-09 22:00:00     3314.83       Sell  -530.0          2  ...            -1.0  3.000000       0           0          -1            0                  2
1829 2025-07-10 06:00:00      3319.19 2025-07-10 08:00:00     3319.19       Sell     0.0          3  ...            -1.0       inf       0           0          -1            0                  1
1830 2025-07-10 09:00:00      3321.14 2025-07-10 09:00:00     3325.29       Sell  -415.0          3  ...            -1.0  3.002410       0           0          -1            0                  2
1831 2025-07-10 11:00:00      3321.08 2025-07-10 11:00:00     3329.65       Sell  -857.0          3  ...            -1.0  3.001167       0           0          -1            0                  2
1832 2025-07-10 15:00:00      3320.46 2025-07-10 17:00:00     3320.46       Sell     0.0          3  ...            -1.0       inf       0           0          -1            0                  1

[1833 rows x 338 columns]
stats : {'buy': {'win_rate': 21.94, 'expectancy': -67.426}, 'sell': {'win_rate': 20.39, 'expectancy': -51.116}, 'buy_sell': {'win_rate': 21.19, 'expectancy': -59.584}}
features : 50
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2021-01-28 ถึง 2023-10-19 (995 วัน, 1099 records)
Val: 2023-10-19 ถึง 2024-08-22 (309 วัน, 366 records)
Test: 2024-08-22 ถึง 2025-07-10 (323 วัน, 368 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 332

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              159       8.67%
SL Hit              1674      91.33%
Technical Exit      0         0.00%
SL + Tech Exit      1674      91.33%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 1356.57
ขาดทุนเฉลี่ยเมื่อ SL Hit: -155.72
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:8.71

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 1674
- อัตราส่วน: 91.33%
- กำไร/ขาดทุนเฉลี่ย: -155.72
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:8.71

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count       mean      sum      mean
DayOfWeek                                     
Monday       390 -46.830769 -18264.0  0.076923
Tuesday      359 -30.590529 -10982.0  0.083565
Wednesday    368 -15.975543  -5879.0  0.092391
Thursday     319  -2.667712   -851.0  0.094044
Friday       397 -22.695214  -9010.0  0.088161

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count        mean     sum      mean
Hour                                     
3       185  -37.405405 -6920.0  0.075676
4       143   47.475524  6789.0  0.132867
5       135  -47.666667 -6435.0  0.081481
6       123   -2.910569  -358.0  0.121951
7       118  -70.754237 -8349.0  0.084746
8       119  -81.285714 -9673.0  0.050420
9       104   88.346154  9188.0  0.096154
10      119  -42.957983 -5112.0  0.058824
11       95  -53.715789 -5103.0  0.063158
12      109   66.972477  7300.0  0.128440
13      104  -73.346154 -7628.0  0.105769
14       98  -88.622449 -8685.0  0.061224
15       59 -111.457627 -6576.0  0.050847
16       56   87.875000  4921.0  0.089286
17       47   22.106383  1039.0  0.106383
18       60   11.300000   678.0  0.100000
19       86  -42.279070 -3636.0  0.058140
20       73  -88.027397 -6426.0  0.082192
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GOLD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (26546, 332), trade_df.shape = (1833, 338)

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 1833 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 342
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 50
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC']
✅ ใช้ Final selected feature: 50 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GOLD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 14023 (52.8%)
  downtrend: 10359 (39.0%)
  sideways: 2164 (8.2%)

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 26546 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 24382/26546 rows (91.8%)
📊 ข้อมูลหลังกรอง trend_following: 24382 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ trend_following: {0.0: 22899, 2.0: 484, 3.0: 476, 1.0: 451, 4.0: 72}
✅ เตรียมข้อมูล trend_following: 24382 samples, 50 features
✅ ข้อมูลพร้อม: X.shape=(24382, 50), y.shape=(24382,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 24382 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 24382 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 22899, 2.0: 484, 3.0: 476, 1.0: 451, 4.0: 72}
📈 Train: 14628, Val: 4877, Test: 4877
📊 Train class distribution: {0.0: 13738, 2.0: 290, 3.0: 286, 1.0: 271, 4.0: 43}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ trend_following
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
📊 Trend Following Parameters (เน้นความเสถียรและ Win Rate):
   learning_rate: [0.02, 0.025, 0.03, 0.035] (CV=18.9% - High Stability)
   num_leaves: [18, 20, 22, 25] (Mean=18.69 - เพิ่มความซับซ้อน)
   feature_fraction: [0.82, 0.84, 0.86] (CV=2.5% - Very High Stability)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.21295676226524968, 1.0: 10.795571955719558, 2.0: 10.088275862068965, 3.0: 10.22937062937063, 4.0: 68.03720930232558}
📊 trend_following Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.037 - 0.087 (base: 0.062)
   num_leaves: 21 - 27 (base: 24)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
🔍 Classes ใน training data: [0.0, 1.0, 2.0, 3.0, 4.0]
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=accuracy
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ trend_following...
✅ Best parameters สำหรับ trend_following: {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}
✅ Best CV score สำหรับ trend_following: 0.7525
💾 บันทึก best parameters สำหรับ trend_following ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ trend_following

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
11267              0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0    21                      -1.0        0.0         1218.0           1.0
12202              0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0    15                      -1.0        1.0        10379.0           1.0
8330               1.0         0.0         0.0     0.0                -1         0.0         1.0  ...           -1              1.0    17                       1.0        0.0         6631.0          -1.0
17528              0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              1.0    20                      -1.0        0.0         1495.0           0.0
4552               0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              1.0    16                      -1.0        0.0         3710.0           0.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following
📊 Class distribution: {0.0: 13738, 1.0: 271, 2.0: 290, 3.0: 286, 4.0: 43}
📊 Imbalance ratio: 0.003
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 13738, 2.0: 290, 3.0: 286, 1.0: 271, 4.0: 43}
🔄 Oversampling class 4.0: 43 -> 200 (+157 samples)
📊 Class distribution หลัง oversample: {0.0: 13738, 2.0: 290, 3.0: 286, 1.0: 271, 4.0: 200}
📊 Class distribution หลัง oversample: {0.0: 13738, 1.0: 271, 2.0: 290, 3.0: 286, 4.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's multi_logloss: 0.662328

Accuracy: 0.7154
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      0.73      0.84      4580
         1.0       0.12      0.73      0.21        90
         2.0       0.05      0.16      0.08        97
         3.0       0.11      0.74      0.20        95
         4.0       0.00      0.00      0.00        15

    accuracy                           0.72      4877
   macro avg       0.26      0.47      0.26      4877
weighted avg       0.93      0.72      0.80      4877

Confusion Matrix:
[[3337  445  268  502   28]
 [   8   66   14    2    0]
 [  15   27   16   35    4]
 [  14    2    4   70    5]
 [   4    0    1   10    0]]
📊 Test Set Accuracy: 0.7154, F1-Score: 0.7969
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.8679
✅ trend_following - Accuracy: 0.715, F1: 0.797, AUC: 0.868

🔍 ตรวจสอบคุณภาพโมเดล trend_following...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (4877, 50)
   y_val shape: (4877,), unique: [0. 1. 2. 3. 4.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (4877, 50)
   y_val shape: (4877,), unique: [0. 1. 2. 3. 4.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (4877, 5)
   Multi-class classification detected (5 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (4877,), unique: [0. 1. 2. 3. 4.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (4877,), unique values: [0. 1. 2. 3. 4.]
   y_pred shape: (4877,), unique values: [0. 1. 2. 3. 4.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 1. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 1. 0.]
   Multi-class classification metrics calculated
   Calculated metrics: Acc=0.7160, F1=0.7982, Prec=0.9319, Rec=0.7160
✅ ML Metrics:
   Accuracy: 0.7160
   AUC: 0.8576
   F1: 0.7982
   Precision: 0.9319
   Recall: 0.7160
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 37.40
   Trades: 487

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD MM60 (trend_following) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน save current model metrics
✅ บันทึก metrics ปัจจุบันสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน send model alert

================================================================================
✅ MODEL ALERT - SUCCESS
================================================================================
🕒 เวลา: 2025-09-14 20:24:37
📊 โมเดล: GOLD MM60 (trend_following)
📋 รายละเอียด: โมเดลผ่านการประเมิน - โมเดลดีขึ้น - ปรับปรุงโดยรวม
💾 การบันทึก: ✅ บันทึกแล้ว
📝 เหตุผล: โมเดลดีขึ้น - ปรับปรุงโดยรวม
🏷️ ประเภท: improved
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following)
================================================================================
💾 บันทึกโมเดล: ✅ ใช่
📝 เหตุผล: โมเดลดีขึ้น - ปรับปรุงโดยรวม
🏷️ ประเภท: improved
================================================================================
✅ บันทึกโมเดล trend_following ที่: LightGBM/Multi/models\trend_following\M60_GOLD_trained.pkl
✅ บันทึก features trend_following ที่: LightGBM/Multi/models\trend_following\M60_GOLD_features.pkl
✅ บันทึก scaler trend_following ที่: LightGBM/Multi/models\trend_following\M60_GOLD_scaler.pkl
📊 สร้าง Feature Importance สำหรับ trend_following
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_GOLD_M60 symbol GOLD timeframe M60 (trend_following)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
         Feature   Gain  Split
 MA_Cross_50_200 0.2174 0.0386
            Hour 0.0822 0.0547
   H2_Price_Move 0.0657 0.0784
 H4_Bar_longwick 0.0537 0.0668
  H12_Price_Move 0.0513 0.0686
   Volume_Lag_50 0.0471 0.0610
 Rolling_Close_5 0.0470 0.0610
H12_Bar_longwick 0.0431 0.0590
   Volume_Lag_20 0.0404 0.0552
    Volume_MA_10 0.0403 0.0581
  H8_Price_Range 0.0377 0.0528
       Bar_CL_HL 0.0350 0.0278
  D1_Price_Range 0.0349 0.0528
    Close_Std_10 0.0320 0.0488
  D1_MACD_signal 0.0175 0.0136

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv (ขนาด: 2882 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
         Feature  Importance
   Volume_Lag_50    0.063113
   Volume_Lag_20    0.061055
 Rolling_Close_5    0.059595
   H2_Price_Move    0.058719
 H4_Bar_longwick    0.058377
    Volume_MA_10    0.057091
    Close_Std_10    0.054686
  H12_Price_Move    0.053185
H12_Bar_longwick    0.052334
  D1_Price_Range    0.048406
  H8_Price_Range    0.047241
            Hour    0.041265
          Bar_TL    0.017394
       Bar_CL_HL    0.017255
         Bar_OSB    0.013327

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.94      1.00      0.97      4580
         1.0       1.00      0.01      0.02        90
         2.0       0.00      0.00      0.00        97
         3.0       0.00      0.00      0.00        95
         4.0       0.00      0.00      0.00        15

    accuracy                           0.94      4877
   macro avg       0.39      0.20      0.20      4877
weighted avg       0.90      0.94      0.91      4877

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง trade_df สำหรับ trend_following: 4877 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 340 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 268560 bytes
✅ เทรน trend_following สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 26546 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 2164/26546 rows (8.2%)
📊 ข้อมูลหลังกรอง counter_trend: 2164 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ counter_trend: {0.0: 1888, 2.0: 111, 3.0: 78, 1.0: 73, 4.0: 14}
✅ เตรียมข้อมูล counter_trend: 2164 samples, 50 features
✅ ข้อมูลพร้อม: X.shape=(2164, 50), y.shape=(2164,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 2164 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 2164 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 1888, 2.0: 111, 3.0: 78, 1.0: 73, 4.0: 14}
📈 Train: 1298, Val: 433, Test: 433
📊 Train class distribution: {0.0: 1132, 2.0: 67, 3.0: 47, 1.0: 44, 4.0: 8}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ counter_trend
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
📊 Counter Trend Parameters (เน้นความยืดหยุ่นและ Expected Payoff):
   learning_rate: [0.07, 0.08, 0.09, 0.1] (CV=20.5% - Medium Stability)
   num_leaves: [38, 40, 42, 45] (Mean=40.50 - เพิ่มความซับซ้อน)
   feature_fraction: [0.86, 0.88, 0.9] (CV=2.0% - Very High Stability)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.2293286219081272, 1.0: 5.9, 2.0: 3.8746268656716416, 3.0: 5.523404255319149, 4.0: 32.45}
📊 counter_trend Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.022 - 0.102 (base: 0.062)
   num_leaves: 14 - 34 (base: 24)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
🔍 Classes ใน training data: [0.0, 1.0, 2.0, 3.0, 4.0]
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=accuracy
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ counter_trend...
✅ Best parameters สำหรับ counter_trend: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 45, 'min_data_in_leaf': 9, 'max_depth': 8, 'learning_rate': 0.08, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.78}
✅ Best CV score สำหรับ counter_trend: 0.8567
💾 บันทึก best parameters สำหรับ counter_trend ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ counter_trend

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
25276              0.0         0.0        -1.0     0.0                -1         0.0         0.0  ...           -1              0.0    11                      -1.0        0.0         8286.0          -1.0
4218               0.0         0.0         0.0     0.0                 1         0.0         0.0  ...            0              1.0     4                       1.0        0.0         1297.0           1.0
22021              0.0         0.0         0.0     0.0                 1         0.0         0.0  ...           -1              0.0    17                      -1.0        0.0         5560.0          -1.0
13436              0.0         0.0         0.0     1.0                -1         0.0         0.0  ...            0              0.0     9                       1.0        0.0         4872.0           1.0
26507              0.0         0.0         0.0     1.0                 1         0.0         0.0  ...            0              0.0     8                       1.0        1.0         9633.0           1.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend
📊 Class distribution: {0.0: 1132, 1.0: 44, 2.0: 67, 3.0: 47, 4.0: 8}
📊 Imbalance ratio: 0.007
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 1132, 2.0: 67, 3.0: 47, 1.0: 44, 4.0: 8}
🔄 Oversampling class 2.0: 67 -> 200 (+133 samples)
🔄 Oversampling class 3.0: 47 -> 200 (+153 samples)
🔄 Oversampling class 1.0: 44 -> 200 (+156 samples)
🔄 Oversampling class 4.0: 8 -> 200 (+192 samples)
📊 Class distribution หลัง oversample: {0.0: 1132, 2.0: 200, 3.0: 200, 1.0: 200, 4.0: 200}
📊 Class distribution หลัง oversample: {0.0: 1132, 1.0: 200, 2.0: 200, 3.0: 200, 4.0: 200}
Training until validation scores don't improve for 50 rounds
Early stopping, best iteration is:
[48]	valid_0's multi_logloss: 0.552035

Accuracy: 0.8337
Classification Report:
              precision    recall  f1-score   support

         0.0       0.88      0.94      0.91       378
         1.0       0.00      0.00      0.00        14
         2.0       0.25      0.14      0.18        22
         3.0       0.12      0.06      0.08        16
         4.0       0.00      0.00      0.00         3

    accuracy                           0.83       433
   macro avg       0.25      0.23      0.23       433
weighted avg       0.78      0.83      0.81       433

Confusion Matrix:
[[357   5   9   6   1]
 [ 14   0   0   0   0]
 [ 17   1   3   1   0]
 [ 15   0   0   1   0]
 [  3   0   0   0   0]]
📊 Test Set Accuracy: 0.8337, F1-Score: 0.8071
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.7540
✅ counter_trend - Accuracy: 0.834, F1: 0.807, AUC: 0.754

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (433, 50)
   y_val shape: (433,), unique: [0. 1. 2. 3. 4.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (433, 50)
   y_val shape: (433,), unique: [0. 1. 2. 3. 4.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (433, 5)
   Multi-class classification detected (5 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (433,), unique: [0. 1. 2. 3. 4.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (433,), unique values: [0. 1. 2. 3. 4.]
   y_pred shape: (433,), unique values: [0. 1. 2. 3. 4.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Calculated metrics: Acc=0.8176, F1=0.7971, Prec=0.7811, Rec=0.8176
✅ ML Metrics:
   Accuracy: 0.8176
   AUC: 0.7701
   F1: 0.7971
   Precision: 0.7811
   Recall: 0.8176
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 41.15
   Trades: 43

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD MM60 (counter_trend) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน save current model metrics
✅ บันทึก metrics ปัจจุบันสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน send model alert

================================================================================
✅ MODEL ALERT - SUCCESS
================================================================================
🕒 เวลา: 2025-09-14 20:25:04
📊 โมเดล: GOLD MM60 (counter_trend)
📋 รายละเอียด: โมเดลผ่านการประเมิน - โมเดลดีขึ้น - ปรับปรุงโดยรวม
💾 การบันทึก: ✅ บันทึกแล้ว
📝 เหตุผล: โมเดลดีขึ้น - ปรับปรุงโดยรวม
🏷️ ประเภท: improved
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend)
================================================================================
💾 บันทึกโมเดล: ✅ ใช่
📝 เหตุผล: โมเดลดีขึ้น - ปรับปรุงโดยรวม
🏷️ ประเภท: improved
================================================================================
✅ บันทึกโมเดล counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
✅ บันทึก features counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
✅ บันทึก scaler counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_GOLD_M60 symbol GOLD timeframe M60 (counter_trend)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
         Feature   Gain  Split
   H2_Price_Move 0.0885 0.0766
   Volume_Lag_20 0.0808 0.0764
     BB_Break_HL 0.0780 0.0161
  D1_Price_Range 0.0662 0.0578
 Rolling_Close_5 0.0647 0.0704
    Close_Std_10 0.0570 0.0684
            Hour 0.0542 0.0482
  H12_Price_Move 0.0498 0.0591
 H4_Bar_longwick 0.0478 0.0611
       Bar_CL_HL 0.0465 0.0239
  H8_Price_Range 0.0463 0.0504
H12_Bar_longwick 0.0460 0.0591
   Volume_Lag_50 0.0432 0.0698
    Volume_MA_10 0.0316 0.0564
 MA_Cross_50_200 0.0298 0.0105

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv (ขนาด: 2822 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
         Feature  Importance
   Volume_Lag_20    0.065812
 Rolling_Close_5    0.053560
   H2_Price_Move    0.052929
 H4_Bar_longwick    0.052535
    Close_Std_10    0.050747
            Hour    0.049278
    Volume_MA_10    0.048474
   Volume_Lag_50    0.048138
  H12_Price_Move    0.047942
H12_Bar_longwick    0.046109
  H8_Price_Range    0.044354
  D1_Price_Range    0.043528
          Bar_TL    0.042078
       Bar_CL_HL    0.025828
      H2_Bar_FVG    0.019495

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.87      0.98      0.92       378
         1.0       0.00      0.00      0.00        14
         2.0       0.00      0.00      0.00        22
         3.0       0.00      0.00      0.00        16
         4.0       0.00      0.00      0.00         3

    accuracy                           0.86       433
   macro avg       0.17      0.20      0.18       433
weighted avg       0.76      0.86      0.81       433

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 433 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 335 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 268476 bytes
✅ เทรน counter_trend สำเร็จ

📊 กำลังเทรน Target_Buy...

--- Training for Target_Buy ---

📊 กำลังเทรน trend_following สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 25659 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 23637/25659 rows (92.1%)
📊 ข้อมูลหลังกรอง trend_following: 23637 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ trend_following: {0.0: 23566, 1.0: 71}
✅ เตรียมข้อมูล trend_following: 23637 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 23637 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 23637 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 23566, 1.0: 71}
📈 Train: 14181, Val: 4728, Test: 4728
📊 Train class distribution: {0.0: 14138, 1.0: 43}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Buy (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Buy:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Buy_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Buy_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ trend_following_Buy
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get optimized param dist from analysis
📊 Optimized Parameter Distribution (32 models, Score=0.5484 - เพิ่ม Win Rate):
   🎯 Very High Stability (CV < 10%):
      feature_fraction: [0.82, 0.86, 0.9] (CV=2.6%)
      bagging_fraction: [0.78, 0.84, 0.9] (CV=5.7%)
   📊 High Stability (CV 10-20%):
      max_depth: [5, 6, 7, 8] (CV=16.0%)
   📈 Medium Stability (CV 20-50%):
      num_leaves: [20, 25, 30, 35, 40] (CV=44.2%)
      min_data_in_leaf: [6, 8, 10, 12] (CV=28.4%)
   ⚠️ Low Stability (CV > 50%):
      learning_rate: [0.02, 0.03, 0.05, 0.08] (CV=56.5%)
📊 Default Parameters (ค่าเฉลี่ยรวม 32 models)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 14138, 1.0: 43}
   Imbalance ratio: 328.8:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 trend_following_Buy Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=roc_auc
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ trend_following_Buy...
✅ Best parameters สำหรับ trend_following_Buy: {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 30, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.03, 'feature_fraction': 0.9, 'bagging_freq': 3, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ trend_following_Buy: 0.9047
💾 บันทึก best parameters สำหรับ trend_following_Buy ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Buy_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ trend_following_Buy

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
8364               1.0         0.0         0.0     0.0                 1         0.0         0.0  ...            0              1.0     5                       1.0        0.0         4443.0          -1.0
8187               0.0         0.0         0.0     1.0                -1         0.0         1.0  ...            0              0.0    12                      -1.0        0.0         8477.0           0.0
9242               1.0         0.0         0.0     0.0                 1         0.0         0.0  ...            1              0.0    13                      -1.0        0.0        17441.0           0.0
4388               0.0         0.0         0.0     0.0                 1         0.0         0.0  ...            1              0.0    13                      -1.0        0.0         5359.0           1.0
13526              0.0         0.0        -1.0     0.0                -1         0.0         0.0  ...            0              1.0     7                       1.0        0.0         5788.0           1.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Buy
📊 Class distribution: {0.0: 14138, 1.0: 43}
📊 Imbalance ratio: 0.003
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 14138, 1.0: 43}
🔄 Oversampling class 1.0: 43 -> 200 (+157 samples)
📊 Class distribution หลัง oversample: {0.0: 14138, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 14138, 1.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.129947

Accuracy: 0.9471
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      0.95      0.97      4714
         1.0       0.01      0.14      0.02        14

    accuracy                           0.95      4728
   macro avg       0.50      0.55      0.49      4728
weighted avg       0.99      0.95      0.97      4728

Confusion Matrix:
[[4476  238]
 [  12    2]]
📊 Test Set Accuracy: 0.9471, F1-Score: 0.9700
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.8599
✅ trend_following_Buy - Accuracy: 0.947, F1: 0.970, AUC: 0.860

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Buy...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (4728, 50)
   y_val shape: (4728,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Buy)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (4728, 50)
   y_val shape: (4728,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (4728, 2)
   Binary classification detected
   Final y_pred shape: (4728,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (4728,), unique values: [0. 1.]
   y_pred shape: (4728,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9516, F1=0.0255, Prec=0.0136, Rec=0.2143
✅ ML Metrics:
   Accuracy: 0.9516
   AUC: 0.8465
   F1: 0.0255
   Precision: 0.0136
   Recall: 0.2143
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 47.81
   Trades: 472

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล GOLD MM60 (trend_following_Buy) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - F1 Score 0.0255 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0136 < 0.1
   - Recall 0.2143 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 20:25:21
📊 โมเดล: GOLD MM60 (trend_following_Buy)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0255 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0255 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following_Buy)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0255 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล trend_following_Buy - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0255 < 0.1
📊 สร้าง Feature Importance สำหรับ trend_following_Buy
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Buy_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Buy_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Buy)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
   MA_Cross_50_200 0.2718 0.0436
     H2_Price_Move 0.1830 0.0950
              Hour 0.1005 0.1584
         Bar_CL_HL 0.0971 0.0489
      Close_Std_10 0.0469 0.1080
    H8_Price_Range 0.0442 0.0727
     Volume_Lag_20 0.0239 0.0383
   Rolling_Close_5 0.0233 0.0325
   H4_Bar_longwick 0.0227 0.0606
H12_Price_Strangth 0.0217 0.0208
    D1_Price_Range 0.0208 0.0315
    H12_Price_Move 0.0164 0.0310
     Volume_Lag_50 0.0149 0.0354
  H12_Bar_longwick 0.0144 0.0383
 H2_Price_Strangth 0.0133 0.0092

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Buy_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Buy_feature_importance.csv (ขนาด: 2834 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Buy

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
          Feature  Importance
    H2_Price_Move    0.071999
  Rolling_Close_5    0.070703
    Volume_Lag_50    0.066562
 H12_Bar_longwick    0.062817
    Volume_Lag_20    0.061942
     Volume_MA_10    0.061393
     Close_Std_10    0.060253
  H4_Bar_longwick    0.058207
   D1_Price_Range    0.051288
   H12_Price_Move    0.047283
   H8_Price_Range    0.044702
             Hour    0.029573
H8_Price_Strangth    0.014710
           Bar_TL    0.014427
          Bar_OSB    0.013756

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      4714
         1.0       0.00      0.00      0.00        14

    accuracy                           1.00      4728
   macro avg       0.50      0.50      0.50      4728
weighted avg       0.99      1.00      1.00      4728

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Buy
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Buy
✅ สร้าง trade_df สำหรับ trend_following_Buy: 4728 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Buy
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Buy/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 355 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Buy

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Buy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Buy/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 272802 bytes
✅ เทรน trend_following_Buy สำเร็จ

📊 กำลังเทรน counter_trend สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 25659 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 2022/25659 rows (7.9%)
📊 ข้อมูลหลังกรอง counter_trend: 2022 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ counter_trend: {0.0: 2008, 1.0: 14}
✅ เตรียมข้อมูล counter_trend: 2022 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 2022 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 2022 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 2008, 1.0: 14}
📈 Train: 1212, Val: 405, Test: 405
📊 Train class distribution: {0.0: 1204, 1.0: 8}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Buy (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Buy:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Buy_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Buy_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ counter_trend_Buy
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get optimized param dist from analysis
📊 Optimized Parameter Distribution (32 models, Score=0.5484 - เพิ่ม Win Rate):
   🎯 Very High Stability (CV < 10%):
      feature_fraction: [0.82, 0.86, 0.9] (CV=2.6%)
      bagging_fraction: [0.78, 0.84, 0.9] (CV=5.7%)
   📊 High Stability (CV 10-20%):
      max_depth: [5, 6, 7, 8] (CV=16.0%)
   📈 Medium Stability (CV 20-50%):
      num_leaves: [20, 25, 30, 35, 40] (CV=44.2%)
      min_data_in_leaf: [6, 8, 10, 12] (CV=28.4%)
   ⚠️ Low Stability (CV > 50%):
      learning_rate: [0.02, 0.03, 0.05, 0.08] (CV=56.5%)
📊 Default Parameters (ค่าเฉลี่ยรวม 32 models)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 1204, 1.0: 8}
   Imbalance ratio: 150.5:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 counter_trend_Buy Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=roc_auc
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ counter_trend_Buy...
✅ Best parameters สำหรับ counter_trend_Buy: {'reg_lambda': 0.005, 'reg_alpha': 0.01, 'num_leaves': 35, 'min_data_in_leaf': 6, 'max_depth': 6, 'learning_rate': 0.08, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Buy: 0.8252
💾 บันทึก best parameters สำหรับ counter_trend_Buy ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Buy_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ counter_trend_Buy

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
17159              0.0         0.0         0.0     0.0                -1         0.0         0.0  ...           -1              0.0    19                      -1.0        0.0         3213.0           1.0
17831              0.0         0.0         0.0     0.0                -1         0.0         1.0  ...            0              0.0     3                       1.0        0.0         1589.0           0.0
946                0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0     8                       1.0        1.0         4584.0           0.0
14566              0.0         0.0         0.0     0.0                 1         0.0         0.0  ...            1              0.0    16                      -1.0        0.0         6445.0           1.0
2259               0.0         0.0         0.0     0.0                -1         0.0         0.0  ...           -1              0.0    15                      -1.0        0.0        12738.0          -1.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Buy
📊 Class distribution: {0.0: 1204, 1.0: 8}
📊 Imbalance ratio: 0.007
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 1204, 1.0: 8}
🔄 Oversampling class 1.0: 8 -> 200 (+192 samples)
📊 Class distribution หลัง oversample: {0.0: 1204, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 1204, 1.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[55]	valid_0's binary_logloss: 0.0429438

Accuracy: 0.9877
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      0.99      0.99       402
         1.0       0.25      0.33      0.29         3

    accuracy                           0.99       405
   macro avg       0.62      0.66      0.64       405
weighted avg       0.99      0.99      0.99       405

Confusion Matrix:
[[399   3]
 [  2   1]]
📊 Test Set Accuracy: 0.9877, F1-Score: 0.9885
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.7960
✅ counter_trend_Buy - Accuracy: 0.988, F1: 0.989, AUC: 0.796

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Buy...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (405, 50)
   y_val shape: (405,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend_Buy)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (405, 50)
   y_val shape: (405,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (405, 2)
   Binary classification detected
   Final y_pred shape: (405,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (405,), unique values: [0. 1.]
   y_pred shape: (405,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9901, F1=0.0000, Prec=0.0000, Rec=0.0000
✅ ML Metrics:
   Accuracy: 0.9901
   AUC: 0.6111
   F1: 0.0000
   Precision: 0.0000
   Recall: 0.0000
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.40
   Trades: 40

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล GOLD MM60 (counter_trend_Buy) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - F1 Score 0.0000 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0000 < 0.1
   - Recall 0.0000 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 20:25:31
📊 โมเดล: GOLD MM60 (counter_trend_Buy)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend_Buy)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล counter_trend_Buy - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
📊 สร้าง Feature Importance สำหรับ counter_trend_Buy
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Buy_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Buy_GOLD_M60 symbol GOLD timeframe M60 (counter_trend_Buy)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
           Feature   Gain  Split
            Bar_TL 0.3229 0.0914
     Volume_Lag_50 0.2188 0.1479
   Rolling_Close_5 0.1118 0.1051
           Bar_OSB 0.0787 0.0642
      Volume_MA_10 0.0419 0.0486
H2_Volume_Momentum 0.0357 0.0175
          STO_zone 0.0338 0.0428
     H2_Price_Move 0.0294 0.0642
   H8_Volume_Spike 0.0266 0.0272
   H4_Bar_longwick 0.0181 0.0700
              Hour 0.0137 0.0175
         Bar_CL_HL 0.0111 0.0117
     Volume_Lag_20 0.0098 0.0214
         D1_Bar_TL 0.0095 0.0272
      Close_Std_10 0.0093 0.0370

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_Buy_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_Buy_feature_importance.csv (ขนาด: 2312 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Buy

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
          Feature  Importance
           Bar_TL    0.117640
    H2_Price_Move    0.082157
    Volume_Lag_20    0.071146
  Rolling_Close_5    0.062644
  H4_Bar_longwick    0.061189
    Volume_Lag_50    0.054226
     Close_Std_10    0.044241
      BB_Break_HL    0.036366
   D1_Price_Range    0.032962
     Volume_MA_10    0.032760
   H8_Price_Range    0.030845
 H12_Bar_longwick    0.028101
H8_Price_Strangth    0.027932
          Bar_OSB    0.026311
             Hour    0.026085

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00       402
         1.0       0.00      0.00      0.00         3

    accuracy                           0.99       405
   macro avg       0.50      0.50      0.50       405
weighted avg       0.99      0.99      0.99       405

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Buy
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Buy
✅ สร้าง trade_df สำหรับ counter_trend_Buy: 405 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Buy
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 347 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Buy

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Buy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Buy/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 271648 bytes
✅ เทรน counter_trend_Buy สำเร็จ

📊 กำลังเทรน Target_Sell...

--- Training for Target_Sell ---

📊 กำลังเทรน trend_following สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 25600 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 23584/25600 rows (92.1%)
📊 ข้อมูลหลังกรอง trend_following: 23584 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ trend_following: {0.0: 23524, 1.0: 60}
✅ เตรียมข้อมูล trend_following: 23584 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 23584 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 23584 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 23524, 1.0: 60}
📈 Train: 14150, Val: 4717, Test: 4717
📊 Train class distribution: {0.0: 14114, 1.0: 36}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Sell:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ trend_following_Sell
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get optimized param dist from analysis
📊 Optimized Parameter Distribution (32 models, Score=0.5484 - เพิ่ม Win Rate):
   🎯 Very High Stability (CV < 10%):
      feature_fraction: [0.82, 0.86, 0.9] (CV=2.6%)
      bagging_fraction: [0.78, 0.84, 0.9] (CV=5.7%)
   📊 High Stability (CV 10-20%):
      max_depth: [5, 6, 7, 8] (CV=16.0%)
   📈 Medium Stability (CV 20-50%):
      num_leaves: [20, 25, 30, 35, 40] (CV=44.2%)
      min_data_in_leaf: [6, 8, 10, 12] (CV=28.4%)
   ⚠️ Low Stability (CV > 50%):
      learning_rate: [0.02, 0.03, 0.05, 0.08] (CV=56.5%)
📊 Default Parameters (ค่าเฉลี่ยรวม 32 models)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 14114, 1.0: 36}
   Imbalance ratio: 392.1:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 trend_following_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=roc_auc
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ trend_following_Sell...
✅ Best parameters สำหรับ trend_following_Sell: {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 35, 'min_data_in_leaf': 8, 'max_depth': 5, 'learning_rate': 0.02, 'feature_fraction': 0.9, 'bagging_freq': 5, 'bagging_fraction': 0.78}
✅ Best CV score สำหรับ trend_following_Sell: 0.8585
💾 บันทึก best parameters สำหรับ trend_following_Sell ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ trend_following_Sell

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
24024              0.0         0.0         0.0     0.0                 1         1.0         0.0  ...            0              1.0    22                      -1.0        0.0         3376.0          -1.0
8962               0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0     9                       1.0        0.0         2327.0          -1.0
1404               0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              1.0     7                       1.0        0.0         5127.0          -1.0
544                0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              1.0    21                      -1.0        0.0         2710.0          -1.0
17668              0.0         0.0         0.0     1.0                -1         0.0         1.0  ...            0              0.0    22                      -1.0        0.0         2061.0           1.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Sell
📊 Class distribution: {0.0: 14114, 1.0: 36}
📊 Imbalance ratio: 0.003
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 14114, 1.0: 36}
🔄 Oversampling class 1.0: 36 -> 200 (+164 samples)
📊 Class distribution หลัง oversample: {0.0: 14114, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 14114, 1.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.185211

Accuracy: 0.9262
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      0.93      0.96      4705
         1.0       0.01      0.33      0.02        12

    accuracy                           0.93      4717
   macro avg       0.50      0.63      0.49      4717
weighted avg       1.00      0.93      0.96      4717

Confusion Matrix:
[[4365  340]
 [   8    4]]
📊 Test Set Accuracy: 0.9262, F1-Score: 0.9593
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.8734
✅ trend_following_Sell - Accuracy: 0.926, F1: 0.959, AUC: 0.873

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (4717, 50)
   y_val shape: (4717,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Sell)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (4717, 50)
   y_val shape: (4717,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (4717, 2)
   Binary classification detected
   Final y_pred shape: (4717,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (4717,), unique values: [0. 1.]
   y_pred shape: (4717,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 1]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9328, F1=0.0246, Prec=0.0128, Rec=0.3333
✅ ML Metrics:
   Accuracy: 0.9328
   AUC: 0.8922
   F1: 0.0246
   Precision: 0.0128
   Recall: 0.3333
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 46.97
   Trades: 471

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล GOLD MM60 (trend_following_Sell) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - F1 Score 0.0246 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0128 < 0.1
   - Recall 0.3333 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 20:25:46
📊 โมเดล: GOLD MM60 (trend_following_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0246 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0246 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0246 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล trend_following_Sell - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0246 < 0.1
📊 สร้าง Feature Importance สำหรับ trend_following_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Sell_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
         Feature   Gain  Split
 MA_Cross_50_200 0.3243 0.0497
            Hour 0.1520 0.2003
       Bar_CL_HL 0.0802 0.0403
H12_Bar_longwick 0.0767 0.1115
  H12_Price_Move 0.0565 0.1253
   H2_Price_Move 0.0560 0.0740
          Bar_TL 0.0414 0.0524
 Rolling_Close_5 0.0365 0.0480
     BB_Break_HL 0.0282 0.0326
  D1_Price_Range 0.0195 0.0315
    Volume_MA_10 0.0179 0.0403
  D1_MACD_signal 0.0179 0.0193
      H12_Bar_TL 0.0143 0.0127
   Volume_Lag_20 0.0141 0.0270
   Volume_Lag_50 0.0109 0.0232

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv (ขนาด: 2513 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
          Feature  Importance
    H2_Price_Move    0.072522
    Volume_Lag_50    0.071650
     Volume_MA_10    0.070520
  Rolling_Close_5    0.063767
 H12_Bar_longwick    0.062304
    Volume_Lag_20    0.061205
     Close_Std_10    0.057738
   H12_Price_Move    0.055075
   D1_Price_Range    0.049694
  H4_Bar_longwick    0.043447
   H8_Price_Range    0.042949
             Hour    0.026951
           Bar_TL    0.026355
       H4_Bar_OSB    0.013819
H2_Price_Strangth    0.013706

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      4705
         1.0       0.00      0.00      0.00        12

    accuracy                           1.00      4717
   macro avg       0.50      0.50      0.50      4717
weighted avg       0.99      1.00      1.00      4717

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง trade_df สำหรับ trend_following_Sell: 4717 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 358 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 269975 bytes
✅ เทรน trend_following_Sell สำเร็จ

📊 กำลังเทรน counter_trend สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 25600 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 2016/25600 rows (7.9%)
📊 ข้อมูลหลังกรอง counter_trend: 2016 samples

🛠️ Features used for training (Selected: 50 total):
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
📊 Class distribution สำหรับ counter_trend: {0.0: 2002, 1.0: 14}
✅ เตรียมข้อมูล counter_trend: 2016 samples, 50 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 2016 samples, 50 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 2016 samples, 50 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 2002, 1.0: 14}
📈 Train: 1209, Val: 403, Test: 404
📊 Train class distribution: {0.0: 1201, 1.0: 8}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Sell:
   do_hyperparameter_tuning = True
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_best_params.json

🔍 เริ่ม Hyperparameter Tuning ด้วย RandomizedSearchCV สำหรับ counter_trend_Sell
🔧 ใช้ Scenario-Specific Parameter Distribution (แม่นยำที่สุด)
🏗️ เปิดใช้งาน get scenario specific param dist
🏗️ เปิดใช้งาน get optimized param dist from analysis
📊 Optimized Parameter Distribution (32 models, Score=0.5484 - เพิ่ม Win Rate):
   🎯 Very High Stability (CV < 10%):
      feature_fraction: [0.82, 0.86, 0.9] (CV=2.6%)
      bagging_fraction: [0.78, 0.84, 0.9] (CV=5.7%)
   📊 High Stability (CV 10-20%):
      max_depth: [5, 6, 7, 8] (CV=16.0%)
   📈 Medium Stability (CV 20-50%):
      num_leaves: [20, 25, 30, 35, 40] (CV=44.2%)
      min_data_in_leaf: [6, 8, 10, 12] (CV=28.4%)
   ⚠️ Low Stability (CV > 50%):
      learning_rate: [0.02, 0.03, 0.05, 0.08] (CV=56.5%)
📊 Default Parameters (ค่าเฉลี่ยรวม 32 models)
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 1201, 1.0: 8}
   Imbalance ratio: 150.1:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 counter_trend_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
🔍 RandomizedSearchCV Settings:
   n_iter=12, cv=StratifiedKFold(3), scoring=roc_auc
   scenario_specific=True, optimized=False
🔍 กำลังทำ RandomizedSearchCV สำหรับ counter_trend_Sell...
✅ Best parameters สำหรับ counter_trend_Sell: {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 12, 'max_depth': 7, 'learning_rate': 0.05, 'feature_fraction': 0.82, 'bagging_freq': 5, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Sell: 0.6634
💾 บันทึก best parameters สำหรับ counter_trend_Sell ที่: LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_Sell_best_params.json
🔒 ปิด tuning ในครั้งถัดไป สำหรับ counter_trend_Sell

--- Features (Columns) ---
['D1_Volume_Spike', 'H4_Bar_DTB', 'H2_Bar_FVG', 'Bar_TL', 'RSI_signal_EMA12', 'D1_Bar_FVG', 'H12_Bar_TL', 'IsMorning', 'H12_Price_Strangth', 'H12_Bar_CL_OC', 'EMA_Cross_EMA10', 'ATR_Deep', 'Bar_OSB', 'STO_zone', 'Volume_Lag_50', 'H2_Volume_Spike', 'H12_Bar_SW', 'H8_Volume_TrendStrength', 'MA_Cross_50_200', 'H8_Price_Strangth', 'H12_Price_Move', 'H4_Bar_CL_OC', 'H8_Volume_Momentum', 'H2_Volume_Momentum', 'H2_Price_Move', 'H12_Bar_FVG', 'H12_Bar_longwick', 'EMA_Cross_EMA5', 'D1_Price_Range', 'H4_Volume_Momentum', 'H8_Price_Range', 'Bar_CL_HL', 'H4_Bar_longwick', 'D1_MACD_signal', 'H4_Bar_OSB', 'H4_Bar_SW', 'Close_Std_10', 'H2_Price_Strangth', 'Volume_Momentum', 'H4_Volume_TrendStrength', 'Volume_MA_10', 'H8_Bar_TL', 'Rolling_Close_5', 'BB_Break_HL', 'H8_Volume_Spike', 'Hour', 'H12_Volume_TrendStrength', 'D1_Bar_TL', 'Volume_Lag_20', 'H8_Bar_CL_OC']
--- Sample Data (First 5 rows) ---
       D1_Volume_Spike  H4_Bar_DTB  H2_Bar_FVG  Bar_TL  RSI_signal_EMA12  D1_Bar_FVG  H12_Bar_TL  ...  BB_Break_HL  H8_Volume_Spike  Hour  H12_Volume_TrendStrength  D1_Bar_TL  Volume_Lag_20  H8_Bar_CL_OC
11316              0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0     1                       1.0        0.0         3182.0          -1.0
14888              0.0         0.0         0.0     1.0                -1         0.0         0.0  ...           -1              0.0    18                      -1.0        0.0         2845.0          -1.0
7207               0.0         0.0         0.0     0.0                 1         0.0         1.0  ...            0              0.0    19                      -1.0        0.0         5773.0           1.0
22485              0.0         0.0        -1.0     0.0                -1         1.0         1.0  ...            0              1.0    20                      -1.0        0.0         1745.0          -1.0
8209               0.0         0.0         0.0     0.0                -1         0.0         0.0  ...            0              0.0    11                       1.0        1.0         4471.0           0.0

[5 rows x 50 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Sell
📊 Class distribution: {0.0: 1201, 1.0: 8}
📊 Imbalance ratio: 0.007
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 1201, 1.0: 8}
🔄 Oversampling class 1.0: 8 -> 200 (+192 samples)
📊 Class distribution หลัง oversample: {0.0: 1201, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 1201, 1.0: 200}
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.0536535

Accuracy: 0.9901
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00       401
         1.0       0.00      0.00      0.00         3

    accuracy                           0.99       404
   macro avg       0.50      0.50      0.50       404
weighted avg       0.99      0.99      0.99       404

Confusion Matrix:
[[400   1]
 [  3   0]]
📊 Test Set Accuracy: 0.9901, F1-Score: 0.9876
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.4356
✅ counter_trend_Sell - Accuracy: 0.990, F1: 0.988, AUC: 0.436

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (403, 50)
   y_val shape: (403,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend_Sell)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (403, 50)
   y_val shape: (403,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (403, 2)
   Binary classification detected
   Final y_pred shape: (403,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (403,), unique values: [0. 1.]
   y_pred shape: (403,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9901, F1=0.0000, Prec=0.0000, Rec=0.0000
✅ ML Metrics:
   Accuracy: 0.9901
   AUC: 0.4621
   F1: 0.0000
   Precision: 0.0000
   Recall: 0.0000
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.46
   Trades: 40

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล GOLD MM60 (counter_trend_Sell) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - AUC 0.4621 < 0.5
   - F1 Score 0.0000 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0000 < 0.1
   - Recall 0.0000 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 20:25:57
📊 โมเดล: GOLD MM60 (counter_trend_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: AUC 0.4621 < 0.5, F1 Score 0.0000 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: AUC 0.4621 < 0.5, F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: AUC 0.4621 < 0.5, F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล counter_trend_Sell - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: AUC 0.4621 < 0.5, F1 Score 0.0000 < 0.1
📊 สร้าง Feature Importance สำหรับ counter_trend_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Sell_GOLD_M60 symbol GOLD timeframe M60 (counter_trend_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
          Feature   Gain  Split
  Rolling_Close_5 0.2555 0.0958
     Volume_MA_10 0.2001 0.1188
    H2_Price_Move 0.1646 0.1155
     Close_Std_10 0.1152 0.1188
        Bar_CL_HL 0.0382 0.0287
 H12_Bar_longwick 0.0321 0.0459
             Hour 0.0246 0.0303
H2_Price_Strangth 0.0207 0.0197
       H4_Bar_OSB 0.0204 0.0180
      BB_Break_HL 0.0204 0.0311
  H2_Volume_Spike 0.0203 0.0205
  H4_Bar_longwick 0.0171 0.0393
   H8_Price_Range 0.0165 0.0483
   D1_Price_Range 0.0114 0.0663
    Volume_Lag_20 0.0101 0.0418

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_Sell_feature_importance.csv (ขนาด: 2476 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
          Feature  Importance
   H8_Price_Range    0.105797
  Rolling_Close_5    0.093959
     Close_Std_10    0.069205
     Volume_MA_10    0.068370
   D1_Price_Range    0.061892
    Volume_Lag_50    0.057954
             Hour    0.052045
   H12_Price_Move    0.045456
    H2_Price_Move    0.043395
 H12_Bar_longwick    0.041151
  H4_Bar_longwick    0.037758
    Volume_Lag_20    0.033850
      BB_Break_HL    0.031483
H2_Price_Strangth    0.020348
           Bar_TL    0.019028

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00       401
         1.0       0.00      0.00      0.00         3

    accuracy                           0.99       404
   macro avg       0.50      0.50      0.50       404
weighted avg       0.99      0.99      0.99       404

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง trade_df สำหรับ counter_trend_Sell: 404 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 351 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 270586 bytes
✅ เทรน counter_trend_Sell สำเร็จ

✅ เทรนเสร็จสิ้น: 6 โมเดล
🔍 Debug: ผลลัพธ์การเทรน:
  ✅ trend_following: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Buy: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Buy: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Sell: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Sell: มีผลลัพธ์
    📊 Feature Importance: True

📊 บันทึก Random Forest Feature Importance

🏗️ เปิดใช้งาน save combined random forest importance
📊 รวบรวม RF Feature Importance จาก trend_following
📊 รวบรวม RF Feature Importance จาก counter_trend
📊 รวบรวม RF Feature Importance จาก trend_following_Buy
📊 รวบรวม RF Feature Importance จาก counter_trend_Buy
📊 รวบรวม RF Feature Importance จาก trend_following_Sell
📊 รวบรวม RF Feature Importance จาก counter_trend_Sell
🔍 Debug: คอลัมน์ใน combined_rf_df: ['Feature', 'Importance', 'Scenario']
💾 บันทึก Random Forest Feature Importance: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv
📊 รวมจาก 6 scenarios, 50 features

🏆 Top 10 Random Forest Features:
  Rolling_Close_5: Importance=0.0674
  H2_Price_Move: Importance=0.0636
  Volume_Lag_50: Importance=0.0603
  Volume_Lag_20: Importance=0.0592
  Volume_MA_10: Importance=0.0564
  Close_Std_10: Importance=0.0561
  H8_Price_Range: Importance=0.0526
  H4_Bar_longwick: Importance=0.0519
  H12_Bar_longwick: Importance=0.0488
  D1_Price_Range: Importance=0.0480

📊 สร้าง Combined Feature Importance จากทุก scenarios

🏗️ เปิดใช้งาน create combined feature importance
📊 รวบรวม Feature Importance จาก trend_following
📊 รวบรวม Feature Importance จาก counter_trend
📊 รวบรวม Feature Importance จาก trend_following_Buy
📊 รวบรวม Feature Importance จาก counter_trend_Buy
📊 รวบรวม Feature Importance จาก trend_following_Sell
📊 รวบรวม Feature Importance จาก counter_trend_Sell
💾 บันทึก Combined Feature Importance: LightGBM/Multi/results/M60\M60_GOLD_feature_importance.csv
📊 รวมจาก 6 scenarios, 50 features

🏆 Top 10 Features (Combined):
  MA_Cross_50_200: Gain=0.1407, Split=0.0250, Count=6
  H2_Price_Move: Gain=0.0979, Split=0.0839, Count=6
  Rolling_Close_5: Gain=0.0898, Split=0.0688, Count=6
  Hour: Gain=0.0712, Split=0.0849, Count=6
  Bar_TL: Gain=0.0671, Split=0.0311, Count=6
  Volume_MA_10: Gain=0.0570, Split=0.0593, Count=6
  Volume_Lag_50: Gain=0.0562, Split=0.0610, Count=6
  Bar_CL_HL: Gain=0.0514, Split=0.0302, Count=6
  Close_Std_10: Gain=0.0436, Split=0.0646, Count=6
  H12_Bar_longwick: Gain=0.0361, Split=0.0539, Count=6

🔍 ทำ Cross-Validation และ Threshold Optimization สำหรับ Multi-Model
📊 ทำ Time Series Cross-Validation...

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=3792, total_samples=26546

📊 Fold 1/5:
  - Train size: 7586 ตัวอย่าง (28.6% ของข้อมูลทั้งหมด)
  - Val size:   3792 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9377801212760348, 2.0: 0.02372791985235961, 1.0: 0.018455048774057473, 3.0: 0.017795939889269707, 4.0: 0.0022409702082784077}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 5
  📊 Classes: [0. 1. 2. 3. 4.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 5 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.21326960922125388, 1.0: 10.837142857142856, 2.0: 8.428888888888888, 3.0: 11.238518518518518, 4.0: 89.24705882352941}
