
🚀 โหมด: Production/Deploy
   - ใช้โมเดลเดิม
   - ไม่บันทึก ไฟล์โมเดลใหม่
   - ใช้ optimal parameters ที่บันทึกไว้

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)
🔄 Using Multi-Model Architecture
Model base path set to: D:\test_gold\LightGBM\Multi\models
Threshold base path set to: D:\test_gold\LightGBM\Multi
Feature importance path: D:\test_gold\LightGBM\Multi\feature_importance
Available scenarios: ['trend_following', 'counter_trend']
🚀 Starting HTTP Server on http://127.0.0.1:54321
🔄 Using Multi-Model Architecture: True
📁 Model base path: D:\test_gold\LightGBM\Multi\models
📁 Threshold base path: D:\test_gold\LightGBM\Multi
📝 Debug log will be saved to: server_debug.log
 * Serving Flask app 'WebRequest_Server_03_Target'
 * Debug mode: off

[2025-09-15 07:11:45.590254] 📨 Received HTTP POST request
[2025-09-15 07:11:45.590254] 📊 Data received: symbol=GOLD#, timeframe_str=PERIOD_H1, bars_count=1000
[2025-09-15 07:11:45.590254] 🔄 Creating DataFrame from 1000 bars
[2025-09-15 07:11:45.590254] ✅ DataFrame created: (1000, 6)
[2025-09-15 07:11:45.590254] 📊 Columns: ['time', 'open', 'high', 'low', 'close', 'tick_volume']
[2025-09-15 07:11:45.590254] ✅ All required columns present
[2025-09-15 07:11:45.590254] 🕒 Converting timestamps to datetime
[2025-09-15 07:11:45.605856] ✅ Timestamps converted, time range: 2025-09-15 07:00:00+00:00 to 2025-07-15 18:00:00+00:00
[2025-09-15 07:11:45.605856] ✅ Data sorted by time

[2025-09-15 07:11:45.605856] Initializing data store for GOLD (PERIOD_H1)
[2025-09-15 07:11:45.605856] Replaced data store for GOLD (PERIOD_H1) with 1000 bars from batch.
[2025-09-15 07:11:45.605856] 🚀 Starting data processing for GOLD M60
[2025-09-15 07:11:45.605856] 🔄 Creating processing thread for GOLD M60

[2025-09-15 07:11:45.621480] 🔄 Starting process_data_and_trade for GOLD M60
[2025-09-15 07:11:45.621480] 📊 Retrieved 1000 bars from data store
[2025-09-15 07:11:45.621480] 🕒 Latest bar time: 2025-09-15 07:00:00+00:00
[2025-09-15 07:11:45.621480] 🔄 Renaming columns for indicator calculation
[2025-09-15 07:11:45.621480] 🔍 Checking for duplicate columns
[2025-09-15 07:11:45.621480] ✅ Final columns: ['Open', 'High', 'Low', 'Close', 'Volume']
[2025-09-15 07:11:45.621480] ✅ All required price columns present
📊 Loading Multi-Model components for GOLD M60

🏗️ เปิดใช้งาน load scenario models
🔍 กำลังโหลดโมเดลสำหรับ GOLD M60
📁 Base folder: LightGBM/Multi/models

🔍 ตรวจสอบ trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 50 features

🔍 ตรวจสอบ counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 50 features

📊 สรุปการโหลดโมเดล: 2/2 โมเดล
✅ โหลดโมเดลครบทุก scenarios
✅ Successfully loaded 2 scenario models for GOLD M60

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/60_GOLD_time_filters.pkl
⚠️ ไม่พบไฟล์ time filters ที่ LightGBM/Multi/thresholds/60_GOLD_time_filters.pkl จะใช้ค่า default (ทุกวัน/ทุกชั่วโมง)
✅ โหลด Multi-Model components สำเร็จ: 2 scenarios
[2025-09-15 07:11:45.683984] 🔄 Starting indicators calculation
ตรวจสอบค่า ก่อนส่ง : symbol GOLD timeframe 60 <class 'int'> num_nBars_SL 6 <class 'int'>

🏗️ เปิดใช้งาน create features
ตรวจสอบค่า : timeframe 60 timeframe_int 60
🔍 ตรวจสอบ columns ข้อมูล df ก่อนเข้า combined : จำนวน 143
['Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'Date', 'Time', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_OP5', 'EMA_OP10', 'EMA_OP15', 'EMA_CL5', 'EMA_CL10', 'EMA_CL15', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'EMA50', 'EMA100', 'EMA200', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_EMA4', 'RSI_EMA8', 'RSI_EMA12', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'EMA12', 'EMA26', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_14', 'DMP_14', 'DMN_14', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'Support_100', 'Resistance_100', 'PullBack_100_Up', 'PullBack_100_Down', 'SL_Buy', 'SL_Sell', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth']
🔍 ตรวจสอบ columns ข้อมูล df หลัง combined : จำนวน 242
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

--- Merging MTF features ---
🔍 ตรวจสอบ columns ข้อมูล base_df ก่อนเข้า MTF features : จำนวน 241
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20']

Last base_df: 2025-09-15 07:00:00+00:00
Last H2: 2025-09-15 06:00:00+00:00
Last H4: 2025-09-15 04:00:00+00:00
Last D1: 2025-09-15 00:00:00+00:00

ตรวจสอบว่า row สุดท้ายยังอยู่หลัง merge:

Last row before merge (df_combined) :                                  Date      Time     Open     High      Low   Close  Volume  ... Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
time                                                                                        ...                                                                                              
2025-09-15 07:00:00+00:00  2025.09.15  07:00:00  3646.05  3646.83  3645.25  3646.4     786  ...      7265.8     3641.995      5.354448        6115.4     3644.553      4.976866       8361.15

[1 rows x 242 columns]

Last row before merge (base_df) :                                  Date      Time     Open     High      Low   Close  Volume  DayOfWeek  ...  Close_Std_5  Volume_MA_5  Close_MA_10  Close_Std_10  Volume_MA_10  Close_MA_20  Close_Std_20  Volume_MA_20
DateTime                                                                                               ...                                                                                                            
2025-09-15 07:00:00+00:00  2025.09.15  07:00:00  3646.05  3646.83  3645.25  3646.4     786          0  ...      5.15122       7265.8     3641.995      5.354448        6115.4     3644.553      4.976866       8361.15

[1 rows x 241 columns]

Last row after merge (df_merged) :                                  Date      Time     Open     High      Low   Close  Volume  ...  D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
DateTime                                                                                    ...                                                                                                                             
2025-09-15 07:00:00+00:00  2025.09.15  07:00:00  3646.05  3646.83  3645.25  3646.4     786  ...                0.0              0.0                -1.0                      1.0           1.0           1.0             1.0

[1 rows x 331 columns]

✅ Final df_merged columns: 332
Index(['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime',
       'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight',
       'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB'],
      dtype='object')
🔎 Last row:                                 999
Date                     2025.09.15
Time                       07:00:00
Open                        3646.05
High                        3646.83
Low                         3645.25
...                             ...
D1_Volume_Momentum             -1.0
D1_Volume_TrendStrength         1.0
D1_MACD_line                    1.0
D1_MACD_deep                    1.0
D1_MACD_signal                  1.0

[332 rows x 1 columns]
🔍 ตรวจสอบ columns ข้อมูล df หลัง mtf : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ สร้าง Indicators และ Features เรียบร้อย (ลบแถวที่ขาดหายไป 204 จาก 1000 แถว)

🔍 ตรวจสอบ Temporal Dependence และคุณสมบัติอนุกรมเวลา
- ข้อมูลเรียงตามเวลา: ใช่ (ควรเป็น 'ใช่')
- ช่วงเวลาข้อมูล: 2025-07-28 15:00:00+00:00 ถึง 2025-09-15 07:00:00+00:00
- ระยะเวลารวม: 48 days 16:00:00
- ช่วงห่างระหว่างบันทึก (เฉลี่ย): 0 days 01:28:09.056603773
- ช่วงห่างระหว่างบันทึก (สูงสุด): 2 days 02:00:00
- ช่วงห่างระหว่างบันทึก (ต่ำสุด): 0 days 01:00:00
- จำนวนช่วงเวลาที่หายไป: 35 (จากทั้งหมด 795 ช่วง)
⚠️ เตือน : พบช่วงเวลาที่หายไปซึ่งอาจส่งผลต่อการวิเคราะห์
- จำนวน timestamp ที่ซ้ำกัน: 0

🔍 ตรวจสอบ Stationarity ของข้อมูล:

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Close:
ADF Statistic: 0.3758
p-value: 0.9806
Critical Values:
   1%: -3.4386
   5%: -2.8652
   10%: -2.5687

🏗️ เปิดใช้งาน check stationarity
📊 ผลการทดสอบ Stationarity สำหรับ Returns:
ADF Statistic: -29.8759
p-value: 0.0000
Critical Values:
   1%: -3.4386
   5%: -2.8652
   10%: -2.5687

💾 บันทึกรายงาน Temporal Analysis ที่: LightGBM/Multi/results\60_GOLD_temporal_report.json

📌 จำนวน Missing Values แสดงเฉพาะคอลัมน์ที่มีค่าว่าง และจำนวน > 0:
Series([], dtype: int64)

📌 จำนวน Missing Values หลังการประมวลผล:
Series([], dtype: int64)

🔍 Unique values in df['DayOfWeek']: [0 1 2 3 4]
🔍 Market Condition: uptrend

🔄 Using Enhanced Model Decision with Target_Buy/Target_Sell confirmation...
🔍 BUY Analysis - Close: 3646.05000, EMA200: 3593.09907, Scenario: trend_following
❌ เกิดข้อผิดพลาดใน enhanced_model_decision_buy: 'NoneType' object has no attribute 'transform'
🔍 SELL Analysis - Close: 3646.05000, EMA200: 3593.09907, Scenario: counter_trend
❌ เกิดข้อผิดพลาดใน enhanced_model_decision_sell: 'NoneType' object has no attribute 'transform'
📊 Enhanced Decision Results:
   BUY: False (Confidence: 0.0000) - Error: 'NoneType' object has no attribute 'transform'
   SELL: False (Confidence: 0.0000) - Error: 'NoneType' object has no attribute 'transform'

🎯 Final Enhanced Decision Analysis:
   BUY Decision: False (Confidence: 0.0000)
   SELL Decision: False (Confidence: 0.0000)
⚪ Selected HOLD (No conditions met, Max confidence: 0.0000)
🤖 Final Multi-Model Decision: HOLD (Confidence: 0.0000, Scenario: enhanced)
🏗️ เปิดใช้งาน load scenario threshold
⚠️ ไม่พบไฟล์ threshold LightGBM/Multi/thresholds/60_GOLD_enhanced_optimal_threshold.pkl สำหรับ enhanced, ใช้ค่า default: 0.25 ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/60_GOLD_enhanced_optimal_threshold.pkl')
🏗️ เปิดใช้งาน load scenario nbars
⚠️ ไม่พบไฟล์ nBars_SL สำหรับ enhanced, ใช้ค่า default: 4 ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/M60_GOLD_enhanced_optimal_nBars_SL.pkl')
🎯 Using enhanced threshold: 0.25
🎯 Using enhanced nBars_SL: 4
[2025-09-15 07:11:48.134135] ⏰ Time filters disabled - Trading allowed anytime

🔍 Technical Analysis Check for GOLD M60:
📊 Model Prediction: HOLD (Confidence: 0.0000)
⚙️ Technical Conditions: ENABLED
[2025-09-15 07:11:48] Symbol: GOLD, Timeframe: 60
📈 Key Features Values:
   Date: 2025.09.15 06:00:00
   Open: 3640.99
   Close: 3646.05
   Volume: 6282
   Volume_MA20: 8361.15
   EMA50: 3640.943202526363
   EMA200: 3593.099070144072
   RSI14: 53.83511014259275
   MACD: -0.4924143253915645
   MACD signal: 0.4327486008461718
   MACD histogram: -0.9251629262377363
   PullBack_Up: 0.24060150375884726
   PullBack_Down: 0.7593984962388742
   Ratio_Buy: 0.5440494590697487
   Ratio_Sell: 33.329999966670734
--------------------------------------------------------------------------------
⚠️ No technical conditions defined for HOLD signal
[2025-09-15 07:11:48.143663] Model confidence (0.0000) below threshold (0.25). No signal potential detected.
[2025-09-15 07:11:48.143663] 60 GOLD Signal HOLD Confidence 0.0000 Waiting Model confidence (0.0000) below threshold (0.25).
🔍 DEBUG - Before storing signal data:
   signal: HOLD
   predicted_signal: HOLD
   probability_tp_hit: 0.0
   type(probability_tp_hit): <class 'float'>
[2025-09-15 07:11:48.145669] Stored latest signal for GOLD (enum: 60): HOLD (0.0000)
[2025-09-15 07:11:48.146617] Finished processing for GOLD (60).
[2025-09-15 07:11:48.146617] ✅ Processing thread completed for GOLD M60
🏗️ เปิดใช้งาน load scenario threshold
⚠️ ไม่พบไฟล์ threshold LightGBM/Multi/thresholds/60_GOLD_trend_following_optimal_threshold.pkl สำหรับ trend_following, ใช้ค่า default: 0.25 ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/60_GOLD_trend_following_optimal_threshold.pkl')
🏗️ เปิดใช้งาน load scenario threshold
⚠️ ไม่พบไฟล์ threshold LightGBM/Multi/thresholds/60_GOLD_counter_trend_optimal_threshold.pkl สำหรับ counter_trend, ใช้ค่า default: 0.25 ([Errno 2] No such file or directory: 'LightGBM/Multi/thresholds/60_GOLD_counter_trend_optimal_threshold.pkl')
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 2
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 11
🔍 DEBUG - Before sending to MT5:
   response_signal: HOLD
   response_confidence: 0.0
   type(response_confidence): <class 'float'>
✅ Logged to: LightGBM/Server_Log\250915_060_GOLD_Log.txt

📤 PYTHON SERVER - Sending JSON to MT5:
   📏 JSON Length: 878 characters
   📊 Confidence in JSON: 0.0
   🎯 Signal: HOLD
   📝 JSON Preview: {"status": "OK", "message": "Latest signal: HOLD (0.0000) for bar at 2025.09.15 07:00", "signal": "H...ng_sell_confidence": 0.0, "counter_trend_buy_confidence": 0.0, "counter_trend_sell_confidence": 0.0}

[2025-09-15 07:11:48.156327] Sending JSON Response to MT5: {'status': 'OK', 'message': 'Latest signal: HOLD (0.0000) for bar at 2025.09.15 07:00', 'signal': 'HOLD', 'class': 'HOLD', 'confidence': 0.0, 'bar_timestamp': 1757919600.0, 'signal_bar_timestamp': 1757919600.0, 'symbol': 'GOLD', 'timeframe_str': 'PERIOD_H1', 'entry_price': 0.0, 'sl_price': 0.0, 'tp_price': 0.0, 'best_entry': 0.0, 'nBars_SL': 4, 'threshold': 0.25, 'time_filters': 'Days:[0, 1, 2, 3, 4, 5, 6],Hours:[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23]', 'spread': 25, 'market_condition': 'uptrend', 'action_type': 'hold', 'scenario_used': 'enhanced', 'trend_following_threshold': 0.25, 'trend_following_nbars': 2, 'counter_trend_threshold': 0.25, 'counter_trend_nbars': 11, 'trend_following_buy_confidence': 0.0, 'trend_following_sell_confidence': 0.0, 'counter_trend_buy_confidence': 0.0, 'counter_trend_sell_confidence': 0.0}
