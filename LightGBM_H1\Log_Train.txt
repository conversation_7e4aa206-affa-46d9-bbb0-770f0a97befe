
🔬 โหมด: Experiment/Development
   - เทรนโมเดลใหม่
   - บันทึก ไฟล์โมเดล
   - ทดสอบ optimal parameters

🏗️ Multi-Model Architecture Directories:
   📁 Main Folder: LightGBM/Multi
   📁 Hyperparameters: LightGBM/Hyper_Multi

🏗️ Creating Multi-Model Architecture Directories:
📁 Exists: LightGBM/Data_Trained (Data Storage)
📁 Exists: LightGBM/Hyper_Multi (Hyperparameters)
📁 Exists: LightGBM/Multi_Time (Time Used Folder)
📁 Exists: LightGBM/Multi (Main Multi-Model)
📁 Exists: LightGBM/Multi/feature_importance (Feature Importance)
📁 Exists: LightGBM/Multi/individual_performance (Performance Analysis)
📁 Exists: LightGBM/Multi/models (Models Base)
📁 Exists: LightGBM/Multi/results (Results)
📁 Exists: LightGBM/Multi/thresholds (Thresholds)
📁 Exists: LightGBM/Multi/training_summaries (Training Summaries)

🏗️ เปิดใช้งาน __name__
🏗️ เปิดใช้งาน run main analysis

================================================================================
🚀 เริ่มการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-14 15:56:13
🔢 จำนวนรอบทั้งหมด: 1
📊 จำนวนกลุ่มข้อมูล: 1
📁 จำนวนไฟล์ทั้งหมด: 2
   - M60: 2 ไฟล์
================================================================================

============================================================
🔄 รอบที่ 1/1
============================================================
⏰ เวลาเริ่มรอบ: 15:56:13
📊 ประมวลผลกลุ่ม M60 (2 ไฟล์)

🏗️ เปิดใช้งาน main
============================================================
🚀 เริ่มต้นการวิเคราะห์ Multi-Model Architecture
============================================================
📋 ขั้นตอนการทำงาน:
   1. โหลดและประมวลผลข้อมูล
   2. เทรนโมเดล Multi-Model Architecture
   3. ทดสอบ Optimal Threshold
   4. ทดสอบ Optimal nBars SL
   5. บันทึกผลลัพธ์และพารามิเตอร์
============================================================
file CSV_Files_Fixed/GOLD_H1_FIXED.csv main_round 1 symbol GOLD timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
threshold (backward compatibility) : threshold 0.65 default 0.3

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 2

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_GOLD_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 2 confidence 0.65

🏗️ เปิดใช้งาน load and process data

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_GOLD_features.pkl
✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: 24 features
1. Bar_CL_HL
2. MACD_line_x_PriceMove
3. IsMorning
4. Volume_Lag_20
5. Momentum5_x_Volatility10
6. IsNight
7. Bar_longwick
8. H12_Price_Move
9. IsEvening
10. IsAfternoon
... และอีก 14 features

First 5 rows of df:
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ GOLD MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ GOLD MM60
✅ trend_following: พร้อมใช้งาน
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
✅ counter_trend: พร้อมใช้งาน
❌ counter_trend_Buy: ไม่พบไฟล์โมเดล
❌ counter_trend_Sell: ไม่พบไฟล์โมเดล

📊 สรุป: 2/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following_Buy', 'trend_following_Sell', 'counter_trend_Buy', 'counter_trend_Sell']
🔍 กำลังโหลดโมเดลสำหรับ GOLD MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 2 scenarios: ['trend_following', 'counter_trend']

🔍 โหลด trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_GOLD_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 24 features

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 23 features

📊 สรุปการโหลดโมเดล: 2/6 โมเดล
⚠️ โหลดโมเดลได้ไม่ครบ - อาจส่งผลต่อประสิทธิภาพการทำนาย
📋 โมเดลที่โหลดได้: ['trend_following', 'counter_trend']
📋 โมเดลที่ขาดหายไป: ['trend_following_Buy', 'trend_following_Sell', 'counter_trend_Buy', 'counter_trend_Sell']
✅ โหลดโมเดล Multi-Model สำเร็จ: ['trend_following', 'counter_trend']
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following_Buy (ใช้จาก trend_following): 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following_Sell (ใช้จาก trend_following): 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Buy (ใช้จาก counter_trend): 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Sell (ใช้จาก counter_trend): 0.6500
🎯 Scenario Thresholds: {'trend_following': 0.65, 'counter_trend': 0.65, 'trend_following_Buy': 0.65, 'trend_following_Sell': 0.65, 'counter_trend_Buy': 0.65, 'counter_trend_Sell': 0.65}

🏗️ เปิดใช้งาน try trade with threshold adjustment

🔧 เริ่มทดสอบ Multi-Model ด้วย reduce_threshold เริ่มต้น 1.0 (threshold จริง: 0.6500)

============================================================
🧪 ครั้งที่ 1: ทดสอบ reduce_threshold = 1.0 (threshold จริง: 0.6500) (Multi-Model)
============================================================
ใช้ Multi-Model พร้อม scenario_thresholds

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)
📊 โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ counter_trend: 0.6500
🎯 Scenario Thresholds: {'trend_following': 0.65, 'counter_trend': 0.65}
📊 ใช้ Multi-Model: ['trend_following', 'counter_trend']
✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 1.0000

🔄 ใช้ Multi-Model Architecture: ['trend_following', 'counter_trend']
📋 Scenario Model Mapping: {'trend_following': 'trend_following', 'counter_trend': 'counter_trend', 'trend_following_Buy': 'trend_following', 'trend_following_Sell': 'trend_following', 'counter_trend_Buy': 'counter_trend', 'counter_trend_Sell': 'counter_trend'}
🔍 ตรวจสอบโมเดลที่โหลดมาแล้ว: ['trend_following', 'counter_trend']
📊 Scenario trend_following ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Buy ใช้: ✅ โมเดล trend_following
📊 Scenario trend_following_Sell ใช้: ✅ โมเดล trend_following
📊 Scenario counter_trend_Buy ใช้: ✅ โมเดล counter_trend
📊 Scenario counter_trend_Sell ใช้: ✅ โมเดล counter_trend
✅ Features สะอาด: 24 features (ไม่มี raw price หรือ data leakage)

🤖 สถานะการใช้งานโมเดลตาม Scenario:
   trend_following: ✅ ใช้โมเดลตัวเอง
   counter_trend: ✅ ใช้โมเดลตัวเอง
   trend_following_Buy: ✅ ใช้โมเดล trend_following
   trend_following_Sell: ✅ ใช้โมเดล trend_following
   counter_trend_Buy: ✅ ใช้โมเดล counter_trend
   counter_trend_Sell: ✅ ใช้โมเดล counter_trend
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 71828 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 71778
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
✅ พบการเทรด 85 รายการด้วย reduce_threshold 1.0 (threshold จริง: 0.6500)
📊 ข้อมูลเพียงพอสำหรับ optimization: พอใช้ (train=51, val=17, test=17)
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.6500 (Multi-Model)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_06a_trade_df.csv

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 71828 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 85 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1373.07       Sell   553.0          1  ...      2.587143          14.024013       34.910969  0.000000    0.004011                1411.3                      1
1 2013-10-29 11:00:00      1344.49 2013-10-29 11:00:00     1344.49        Buy     0.0          1  ...      3.730714          13.603247       36.926356  0.000000    0.009751                2064.1                      1
2 2013-11-06 14:00:00      1318.39 2013-11-06 17:00:00     1318.39       Sell     0.0          2  ...      2.885714          12.637976       61.174521  0.000000    0.008397                1410.0                      1
3 2013-11-14 19:00:00      1289.44 2013-11-15 03:00:00     1289.44       Sell     0.0          3  ...      4.631429          10.395587       62.158558  0.000000    0.011455                2736.3                      1
4 2013-11-21 15:00:00      1247.99 2013-11-21 15:00:00     1240.49       Sell   750.0          3  ...      2.810000          19.760851       39.486723  0.002003    0.006010                1901.5                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                0.00                
Expectancy          0.00                
📈 สถิติสำหรับ Sell Trades:
Win%                38.46               
Expectancy          -67.50              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                38.46               
Expectancy          -67.50              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    25.00          8                   
Tuesday   8.70           23                  
Wednesday 7.14           14                  
Thursday  14.29          21                  
Friday    10.53          19                  
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
5         0.00           1                   
10        42.86          7                   
11        12.50          8                   
12        0.00           3                   
13        0.00           4                   
14        0.00           2                   
15        20.00          10                  
16        0.00           15                  
17        12.50          8                   
18        16.67          12                  
19        0.00           9                   
20        16.67          6                   
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-09-10 10:00:00
1   2013-10-29 11:00:00
2   2013-11-06 14:00:00
3   2013-11-14 19:00:00
4   2013-11-21 15:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-09-10 09:55:00
1   2013-10-29 10:55:00
2   2013-11-06 13:55:00
3   2013-11-14 18:55:00
4   2013-11-21 14:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2013-07-18 12:00:00
1   2013-07-18 13:00:00
2   2013-07-18 14:00:00
3   2013-07-18 15:00:00
4   2013-07-18 16:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 85
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1373.07       Sell   553.0          1          10
1 2013-10-29 11:00:00      1344.49 2013-10-29 11:00:00     1344.49        Buy     0.0          1          11
2 2013-11-06 14:00:00      1318.39 2013-11-06 17:00:00     1318.39       Sell     0.0          2          14
3 2013-11-14 19:00:00      1289.44 2013-11-15 03:00:00     1289.44       Sell     0.0          3          19
4 2013-11-21 15:00:00      1247.99 2013-11-21 15:00:00     1240.49       Sell   750.0          3          15

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_06b_trade_df_merge.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 85/85 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 10 samples (11.8%)
  Class 1 (weak_sell): 58 samples (68.2%)
  Class 2 (no_trade): 16 samples (18.8%)
  Class 3 (weak_buy): 1 samples (1.2%)
✅ Multi-class Target ถูกต้อง: 3 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0    10
1    58
2    16
3     1
Name: count, dtype: int64
Class 0 (strong_sell): 10 trades, Profit range: 453.0 to 1587.0
Class 1 (weak_sell): 58 trades, Profit range: 0.0 to 0.0
Class 2 (no_trade): 16 trades, Profit range: -2586.0 to -206.0
Class 3 (weak_buy): 1 trades, Profit range: 0.0 to 0.0

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_07_trade_df_valid_trades.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    75
1    10
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    1
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    74
1    10
Name: count, dtype: int64

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_08a_trade_df_target.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 85
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time        EMA50       EMA100       EMA200      RSI14
0 2013-09-10 10:00:00  1384.192859  1386.333276  1388.417466  34.910969
1 2013-10-29 11:00:00  1349.618580  1343.650089  1332.588210  36.926356
2 2013-11-06 14:00:00  1314.905968  1319.158796  1323.816183  61.174521
3 2013-11-14 19:00:00  1281.085569  1284.533250  1294.413548  62.158558
4 2013-11-21 15:00:00  1259.132691  1267.370293  1277.382628  39.486723

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    75
1    10
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                     1.000000
H2_Volume_TrendStrength    0.352941
ATR_ROC_i6                 0.345377
ATR_ROC_i8                 0.316953
H4_Price_Strangth          0.277746
                             ...   
RSI_Divergence_i6               NaN
RSI_Divergence_i2               NaN
RSI_counter                     NaN
Price_EMA50_x_RSI_trend         NaN
H4_Bar_DTB                      NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
                      feature  VIF
0     H2_Volume_TrendStrength  inf
1                  ATR_ROC_i6  inf
2                  ATR_ROC_i8  inf
3           H4_Price_Strangth  inf
4                H4_Bar_CL_HL  inf
..                        ...  ...
271                 Bar_CL_OC  inf
272       STOCHd_14_3_3_Lag_5  inf
273           D1_Bar_longwick  inf
274  H12_Volume_TrendStrength  inf
275                 Bar_CL_HL  inf

[276 rows x 2 columns]

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['H2_Volume_TrendStrength', 'ATR_ROC_i6', 'ATR_ROC_i8', 'H4_Price_Strangth', 'H4_Bar_CL_HL', 'H4_MACD_deep', 'ATR_ROC_i4', 'H4_Bar_TL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'Slope_EMA50', 'Price_EMA50', 'H2_Bar_OSB', 'H2_Price_Move', 'MACD_signal_x_RSI14', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'Volume_Lag_30', 'H2_MACD_signal', 'EMA_Cross_EMA15', 'ADX_cross', 'MACD_signal_x_ADX', 'Dist_EMA50', 'RSI14_Lag_2', 'H12_Bar_SW', 'H4_Bar_SW', 'EMA_Cross_EMA10', 'Price_Range', 'H2_Bar_SW', 'PullBack_50_Down', 'PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'RSI14_x_ATR', 'H4_Volume_TrendStrength', 'RSI14_Lag_1', 'ADX_14_Lag_1', 'H12_Bar_CL_HL', 'Volume_Lag_50', 'Volume_Momentum', 'Dist_EMA100', 'RSI_Zone', 'RSI14', 'MA_Cross_100_200', 'H8_MACD_line', 'H2_Price_Range', 'RSI14_x_BBwidth', 'ADX_14_Lag_2', 'DMP_14_Lag_3', 'ATR_Lag_2', 'PullBack_100_Down', 'PullBack_100_Up', 'H2_Volume_Spike', 'H12_MACD_signal', 'STO_cross', 'ATR_Lag_1', 'ADX_14_Lag_5', 'ADX_14_Lag_3', 'Close_Std_10', 'ATR_Lag_3', 'IsMorning', 'H4_Bar_longwick', 'RSI14_Lag_3', 'H4_Bar_CL_OC', 'Price_Strangth', 'BB_width', 'High_Lag_1', 'Open_Lag_1', 'Close_Lag_2', 'High_Lag_2', 'Bar_TL', 'Low_Lag_1', 'Close_Lag_1', 'Close_MA_3', 'Volume_MA20', 'Low_Lag_2', 'Open_Lag_2', 'Close_Lag_3', 'H4_Price_Range', 'Close_MA_5', 'High_Lag_3', 'Close_MA_10', 'Low_Lag_3', 'Close_Lag_15', 'Open_Lag_3', 'Close_MA_20', 'High_Lag_5', 'D1_MACD_signal', 'High_Lag_10', 'Close_Lag_5', 'High_Lag_15', 'Open_Lag_5', 'Low_Lag_15', 'Close_Lag_10', 'Open_Lag_10', 'Low_Lag_5', 'Open_Lag_15', 'Low_Lag_50', 'Low_Lag_10', 'Close_Lag_50', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'Open_Lag_50', 'EMA50_Lag_5', 'High_Lag_50', 'EMA100_Lag_1', 'EMA100_Lag_2', 'Close_Lag_20', 'EMA100_Lag_3', 'EMA200_Lag_1', 'EMA100_Lag_5', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'High_Lag_20', 'Open_Lag_20', 'Low_Lag_20', 'Close_Lag_30', 'High_Lag_30', 'Open_Lag_30', 'Low_Lag_30', 'MACD_12_26_9_Lag_1', 'Dist_EMA200', 'Close_Std_20', 'BB_width_Lag_1', 'D1_Volume_Momentum', 'BB_width_Lag_2', 'BB_width_Lag_3', 'Volume_Lag_1', 'RSI14_x_StochD', 'H2_MACD_line', 'H8_Bar_OSB', 'Volume_Change_1', 'H12_Price_Range', 'Volume_Change_2', 'Rolling_Close_15', 'ADX_14_x_ATR', 'RSI14_x_PullBack_50_Up', 'Close_Std_5', 'D1_MACD_deep', 'Slope_EMA100', 'H8_Price_Range', 'Bar_SW', 'H4_Price_Move', 'RSI14_x_StochK', 'MACD_12_26_9_Lag_2', 'BB_width_Lag_5', 'ADX_zone_15', 'STO_Oversold', 'H8_Bar_CL_HL', 'ADX_14_x_BBwidth', 'DMN_14_Lag_2', 'IsAfternoon', 'H8_Volume_Momentum', 'H4_Volume_Spike', 'D1_Price_Move', 'D1_Price_Strangth', 'EMA50_x_RollingVol5', 'MACDs_12_26_9_Lag_1', 'H12_MACD_line', 'ATR_x_PriceRange', 'H4_Volume_Momentum', 'H2_Bar_TL', 'H8_Volume_Spike', 'Hour', 'ATR_Lag_5', 'H12_MACD_deep', 'DMN_14_Lag_1', 'RSI14_x_Volume', 'H4_Bar_OSB', 'Close_Return_1', 'H12_Bar_longwick', 'Price_Move', 'MACD_12_26_9_Lag_3', 'RSI14_x_PriceMove', 'MACDs_12_26_9_Lag_2', 'H2_Bar_FVG', 'H8_MACD_deep', 'Rolling_Close_5', 'Volume_MA_3', 'MACD_12_26_9_Lag_5', 'DMP_14_Lag_5', 'H12_Bar_TL', 'EMA_diff_x_BBwidth', 'EMA_diff_x_ATR', 'RSI_ROC_i2', 'H12_Bar_CL_OC', 'MACDs_12_26_9_Lag_3', 'BB_Break_HL', 'ATR_Deep', 'ADX_14_x_RollingVol15', 'H8_Price_Strangth', 'H2_MACD_deep', 'STOCHk_14_3_3_Lag_1', 'H4_MACD_line', 'RSI14_Lag_5', 'Volume_Lag_2', 'STOCHk_14_3_3_Lag_2', 'Volume_MA_10', 'MACDs_12_26_9_Lag_5', 'D1_MACD_line', 'H8_MACD_signal', 'Close_Std_3', 'RSI_signal_EMA8', 'Volume_MA_5', 'Close_Return_5', 'STOCHd_14_3_3_Lag_1', 'MA_Cross_50_100', 'DMN_14_Lag_3', 'MACD_line_x_PriceMove', 'STO_overbought', 'STO_zone', 'H8_Bar_longwick', 'RSI_signal_EMA12', 'D1_Price_Range', 'ATR_ROC_i2', 'Volume_Change_5', 'Volume_Lag_5', 'H12_Bar_OSB', 'MA_Cross_50_200', 'EMA_diff_x_RSI14', 'RSI_ROC_i4', 'Volume_Change_3', 'BB_Outside', 'D1_Bar_FVG', 'RSI_x_VolumeSpike', 'Volume_Lag_10', 'RSI_signal_EMA4', 'H4_Bar_FVG', 'RSI_ROC_i6', 'D1_Bar_CL_OC', 'H8_Volume_TrendStrength', 'STOCHd_14_3_3_Lag_2', 'H12_Volume_Momentum', 'STOCHk_14_3_3_Lag_3', 'H4_MACD_signal', 'H8_Price_Move', 'D1_Bar_OSB', 'DMN_14_Lag_5', 'ADX_Deep', 'EMA_diff_100_200', 'EMA_diff_50_200', 'Bar_longwick', 'Momentum5_x_Volatility10', 'H12_Price_Move', 'Close_Return_3', 'Volume_Lag_20', 'RSI_ROC_i8', 'H8_Bar_CL_OC', 'D1_Bar_SW', 'Slope_EMA200', 'Bar_CL', 'D1_Volume_TrendStrength', 'EMA_diff_50_100', 'STOCHd_14_3_3_Lag_3', 'DayOfWeek', 'D1_Bar_CL_HL', 'H2_Volume_Momentum', 'EMA_Cross_EMA5', 'STOCHk_14_3_3_Lag_5', 'Close_Return_2', 'D1_Volume_Spike', 'Volume_Spike', 'H12_Price_Strangth', 'BB_Break_CL', 'ADX_zone_25', 'H12_Volume_Spike', 'Volume_Lag_15', 'Bar_OSB', 'H2_Bar_longwick', 'Volume_Lag_3', 'Bar_CL_OC', 'STOCHd_14_3_3_Lag_5', 'D1_Bar_longwick', 'H12_Volume_TrendStrength', 'Bar_CL_HL']

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: LightGBM/Multi\feature_importance\M60_must_have_features.pkl (22 Features)

featuresasset_feature_importance : len  22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
👍 เพิ่ม Feature ที่จำเป็น 'Bar_longwick' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_20' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'STO_zone' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Bar_CL_HL' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'MACD_line_x_PriceMove' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'H12_Price_Range' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'H12_Price_Move' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_5' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Change_1' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_10' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_30' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Price_Range' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Hour' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'ATR_ROC_i2' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Bar_SW' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Momentum5_x_Volatility10' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Price_Strangth' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 22

✅ Final selected features for training: 22 features
📋 Top 10 features:
1. Bar_longwick (corr: 0.0469)
2. Volume_Lag_20 (corr: 0.0428)
3. STO_zone (corr: 0.0768)
4. Bar_CL_HL (corr: 0.0105)
5. IsMorning (corr: 0.1682)
6. MACD_line_x_PriceMove (corr: 0.0774)
7. IsNight (corr: nan)
8. H12_Price_Range (corr: 0.1388)
9. H12_Price_Move (corr: 0.0444)
10. IsAfternoon (corr: 0.1249)
... และอีก 12 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\GOLD_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 22
1. Bar_longwick
2. Volume_Lag_20
3. STO_zone
4. Bar_CL_HL
5. IsMorning
6. MACD_line_x_PriceMove
7. IsNight
8. H12_Price_Range
9. H12_Price_Move
10. IsAfternoon
11. Volume_Lag_5
12. Volume_Change_1
13. Volume_Lag_10
14. Volume_Lag_30
15. Price_Range
... และอีก 7 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.882353
1    0.117647
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.13
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                          count         mean          std         min          25%          50%          75%           max
Bar_longwick               85.0    -0.286092     1.460568   -4.731707    -0.989247    -0.417778     0.631868      3.921569
Volume_Lag_20              85.0  5845.694118  5227.656586  248.000000  2703.000000  5009.000000  7575.000000  40021.000000
STO_zone                   85.0     0.741176     0.675294   -1.000000     1.000000     1.000000     1.000000      1.000000
Bar_CL_HL                  85.0    -0.211765     0.410984   -1.000000     0.000000     0.000000     0.000000      0.000000
IsMorning                  85.0     0.211765     0.410984    0.000000     0.000000     0.000000     0.000000      1.000000
MACD_line_x_PriceMove      85.0    -1.067412     3.024656  -19.390000    -1.910000    -0.880000     0.360000      6.990000
IsNight                    85.0     0.000000     0.000000    0.000000     0.000000     0.000000     0.000000      0.000000
H12_Price_Range            85.0    14.599647    11.507382    2.900000     9.090000    11.170000    15.900000     82.350000
H12_Price_Move             85.0     3.960118    12.081724  -43.050000    -1.430000     4.280000     8.400000     60.970000
IsAfternoon                85.0     0.364706     0.484204    0.000000     0.000000     0.000000     1.000000      1.000000
Volume_Lag_5               85.0  4177.670588  3114.348970  440.000000  2155.000000  3339.000000  5930.000000  17289.000000
Volume_Change_1            85.0     0.171383     0.440930   -0.419714    -0.163165     0.045042     0.393852      1.537493
Volume_Lag_10              85.0  3006.164706  3237.501994  240.000000  1022.000000  2171.000000  3769.000000  18980.000000
Volume_Lag_30              85.0  3782.376471  3529.324500  473.000000  1672.000000  3014.000000  4814.000000  25434.000000
Price_Range                85.0     5.639412     4.912874    0.930000     2.720000     4.620000     6.650000     30.390000
Hour                       85.0    14.435294     3.238338    4.000000    12.000000    15.000000    17.000000     19.000000
ATR_ROC_i2                 85.0     0.060937     0.116075   -0.395493    -0.003610     0.069297     0.139611      0.413843
Bar_SW                     85.0    -0.258824     0.440588   -1.000000    -1.000000     0.000000     0.000000      0.000000
Momentum5_x_Volatility10   85.0    37.291067   197.196561 -736.345061    -0.260370    10.062534    41.284740   1559.940348
IsEvening                  85.0     0.411765     0.495074    0.000000     0.000000     0.000000     1.000000      1.000000
Price_Strangth             85.0     0.470588     0.525272   -1.000000     0.000000     0.000000     1.000000      1.000000
DayOfWeek                  85.0     2.235294     1.324198    0.000000     1.000000     2.000000     3.000000      4.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
⚠️ พบ 1 คู่ features ที่มีความสัมพันธ์สูง (>0.8)

คู่ features ที่มีความสัมพันธ์สูง:
      Feature 1       Feature 2  Correlation
H12_Price_Range     Price_Range     0.801996
    Price_Range H12_Price_Range     0.801996

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...
การกระจายของ Target ในชุดข้อมูล:

📊 ใช้ Target Column: Target_Multiclass
Train: Target_Multiclass
1    0.666667
2    0.176471
0    0.137255
3    0.019608
Name: proportion, dtype: float64
Val: Target_Multiclass
1    0.705882
2    0.176471
0    0.117647
Name: proportion, dtype: float64
Test: Target_Multiclass
1    0.705882
2    0.235294
0    0.058824
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338
✅ บันทึกไฟล์ train, val, test เรียบร้อย

🏗️ เปิดใช้งาน analyze time filters

📊 Time Filter Analysis for GOLD:
📅 Recommended Days: []
⏰ Recommended Hours: []
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (    Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
0      -0.258154      -0.642498 -2.507133   0.431331   1.802776               0.626417      0.0  ... -1.739322   -0.726895  0.524404                 -0.340442  -0.906327        0.911656  -1.055675
1      -0.857219      -0.202403 -2.507133   0.431331   1.802776               0.193689      0.0  ... -1.416757    1.749695  0.524404                 -0.445020  -0.906327       -2.807901  -1.055675
2      -1.516624       0.103107  0.398862   0.431331  -0.554700               0.357827      0.0  ... -0.449061    0.447233  0.524404                 -0.161371  -0.906327       -0.948122  -0.252101
3      -0.315873      -0.713997  0.398862   0.431331  -0.554700              -0.321108      0.0  ...  1.163765    0.644249  0.524404                 -0.125369   1.103355        0.911656   0.551472
4      -0.206819      -0.038040  0.398862   0.431331  -0.554700               1.282970      0.0  ... -0.126496   -0.228090  0.524404                 -0.204056  -0.906327       -0.948122   0.551472
5      -0.210513      -0.563766  0.398862   0.431331  -0.554700              -0.089823      0.0  ...  0.196069   -3.196487  0.524404                 -0.140861  -0.906327        0.911656   1.355045
6       1.085519      -0.703903  0.398862   0.431331  -0.554700               1.320274      0.0  ...  0.841200    0.306501 -1.906925                  0.177126   1.103355        0.911656  -0.252101
7      -1.007253      -0.671603  0.398862   0.431331  -0.554700               0.753251      0.0  ...  0.518634    0.632396  0.524404                 -0.058455   1.103355        0.911656   1.355045
8       1.123001      -0.665041  0.398862   0.431331   1.802776              -0.164431      0.0  ... -1.739322   -1.364830  0.524404                 -0.217307  -0.906327        0.911656   0.551472
9       0.236127      -0.647882  0.398862  -2.318405  -0.554700              -0.843366      0.0  ...  0.518634    0.477681 -1.906925                 -0.228456   1.103355       -0.948122  -1.055675
10      0.779412      -0.209469  0.398862   0.431331  -0.554700              -0.462864      0.0  ... -0.771627    1.025537  0.524404                 -0.201582  -0.906327        0.911656  -1.055675
11      0.183181      -0.821497  0.398862   0.431331  -0.554700               1.447108      0.0  ...  1.486330   -0.107068  0.524404                 -0.004971   1.103355       -0.948122   0.551472
12     -0.062357      -0.789029  0.398862   0.431331  -0.554700              -0.328569      0.0  ...  1.486330    0.068747  0.524404                 -0.082429   1.103355        0.911656  -0.252101
13     -0.523479      -0.529615  0.398862   0.431331  -0.554700               0.947233      0.0  ...  1.163765    1.751159  0.524404                 -0.008951   1.103355        0.911656   1.355045
14      0.650796      -0.483351  0.398862  -2.318405   1.802776               1.111371      0.0  ... -1.739322   -1.658837  0.524404                 -0.222518  -0.906327       -0.948122   1.355045
15      0.902575      -0.402263  0.398862   0.431331   1.802776              -0.030136      0.0  ... -1.739322   -1.571185 -1.906925                 -0.210449  -0.906327       -0.948122  -1.055675
16     -0.156968      -0.575037  0.398862  -2.318405  -0.554700              -0.209196      0.0  ...  0.196069    0.807531 -1.906925                 -0.183548  -0.906327       -0.948122  -1.055675
17      1.104549      -0.611375 -2.507133   0.431331   1.802776               0.551809      0.0  ... -1.739322   -0.801323 -1.906925                 -0.228948  -0.906327       -0.948122  -0.252101
18     -2.647033      -0.401422  0.398862   0.431331  -0.554700               0.387671      0.0  ...  0.518634    0.974518  0.524404                 -0.215292   1.103355        0.911656   0.551472
19     -0.087893       0.542361  0.398862  -2.318405   1.802776              -0.186813      0.0  ... -1.416757   -0.623900 -1.906925                 -0.201726  -0.906327        0.911656  -1.859248
20      0.703822       1.004663  0.398862   0.431331  -0.554700              -0.664306      0.0  ...  0.518634    0.365555  0.524404                 -0.215854   1.103355        0.911656   1.355045
21     -0.269378       0.609990 -2.507133   0.431331  -0.554700               0.977076      0.0  ...  0.841200    0.797978  0.524404                 -0.237449   1.103355        0.911656   0.551472
22     -1.023387      -0.443816  0.398862   0.431331  -0.554700               0.096698      0.0  ...  1.163765    1.207177  0.524404                 -0.117444   1.103355        0.911656  -1.859248
23      0.602098       0.424766  0.398862   0.431331  -0.554700              -1.141800      0.0  ...  0.196069    1.262991  0.524404                 -0.133664  -0.906327        0.911656   0.551472
24     -0.351226       1.867189  0.398862   0.431331  -0.554700              -0.186813      0.0  ...  0.841200    0.230070  0.524404                 -0.182918   1.103355       -0.948122   1.355045
25      0.044417      -0.285678  0.398862   0.431331  -0.554700              -0.470325      0.0  ...  1.163765   -0.321188  0.524404                 -0.014974   1.103355        0.911656   1.355045
26     -0.042724      -0.020544  0.398862   0.431331  -0.554700              -0.328569      0.0  ... -0.126496   -0.879902  0.524404                 -0.219966  -0.906327       -0.948122  -1.859248
27      0.956007       0.411140  0.398862  -2.318405  -0.554700              -0.074901      0.0  ... -0.771627   -0.545767 -1.906925                 -0.222234  -0.906327       -0.948122  -1.055675
28      0.052905      -0.392674  0.398862   0.431331   1.802776              -0.723993      0.0  ... -1.094192    0.143111  0.524404                 -0.124937  -0.906327       -0.948122   0.551472
29      0.206939       0.100247 -2.507133  -2.318405  -0.554700               1.693316      0.0  ...  0.196069    0.461988 -1.906925                 -0.251061  -0.906327       -0.948122   1.355045
30      0.041990      -0.553672  0.398862   0.431331   1.802776               0.738330      0.0  ... -1.094192   -0.803951  0.524404                 -0.146881  -0.906327        0.911656  -1.055675
31     -0.214766      -0.408487  0.398862   0.431331  -0.554700               1.290431      0.0  ...  1.486330   -0.092344  0.524404                  0.489724   1.103355       -0.948122  -1.055675
32      0.512846       0.061890  0.398862   0.431331  -0.554700              -1.179104      0.0  ... -0.771627   -1.244381  0.524404                 -0.193972  -0.906327        0.911656  -0.252101
33     -2.675589       0.202027  0.398862   0.431331  -0.554700               0.454818      0.0  ... -0.126496   -0.642963  0.524404                 -0.176529  -0.906327        0.911656  -0.252101
34      0.787389       0.089311  0.398862  -2.318405  -0.554700              -0.567316      0.0  ... -0.771627   -1.544308 -1.906925                 -0.190340  -0.906327       -0.948122   0.551472
35     -1.597929      -0.397721  0.398862   0.431331  -0.554700               1.753002      0.0  ...  0.841200    0.807673  0.524404                  6.949571   1.103355        0.911656  -1.055675
36      0.928080      -0.558551 -2.507133   0.431331   1.802776               0.827859      0.0  ... -1.416757    0.181501  0.524404                 -0.241806  -0.906327        0.911656  -1.055675
37     -0.111001       0.330725  0.398862  -2.318405  -0.554700              -1.559606      0.0  ... -0.126496   -0.634755  0.524404                 -0.181546  -0.906327       -0.948122   0.551472
38     -2.131700      -0.007590  0.398862   0.431331  -0.554700               0.260836      0.0  ...  0.518634   -0.122142  0.524404                 -0.199488   1.103355       -0.948122   0.551472
39      0.724866       0.289339  0.398862   0.431331  -0.554700              -1.305938      0.0  ... -0.126496   -0.234601  0.524404                  0.110913  -0.906327       -0.948122  -1.055675
40      1.199049      -0.021385  0.398862   0.431331   1.802776              -0.380795      0.0  ... -1.094192   -0.703743  0.524404                 -0.228271  -0.906327       -0.948122  -1.055675
41      0.005860      -0.437255 -2.507133   0.431331  -0.554700               1.932062      0.0  ...  1.163765    0.892441  0.524404                 -0.832273   1.103355        0.911656  -0.252101
42      0.007813      -0.105165  0.398862   0.431331  -0.554700              -1.134339      0.0  ...  0.196069    0.968576  0.524404                  0.247769  -0.906327        0.911656   1.355045
43     -0.352390      -0.427666  0.398862   0.431331  -0.554700               0.059394      0.0  ...  0.841200   -0.277888  0.524404                  0.002638   1.103355       -0.948122   0.551472
44     -1.123917      -0.332278  0.398862   0.431331  -0.554700              -0.447942      0.0  ...  0.841200    0.794383  0.524404                 -0.076750   1.103355        0.911656   1.355045
45      2.913977       0.026056  0.398862   0.431331  -0.554700              -0.164431      0.0  ...  0.841200   -0.053921  0.524404                  0.185213   1.103355        0.911656   1.355045
46      0.137796      -0.012133  0.398862   0.431331  -0.554700              -2.842869      0.0  ...  0.841200    0.138269 -1.906925                  0.148310   1.103355       -0.948122  -1.055675
47      0.898301      -0.093725  0.398862   0.431331  -0.554700              -0.552394      0.0  ...  0.518634    1.252294  0.524404                 -0.163290   1.103355        0.911656  -0.252101
48      0.297162       1.571100  0.398862   0.431331  -0.554700              -2.827947      0.0  ...  0.841200    1.673997  0.524404                 -0.075965   1.103355        0.911656  -0.252101
49      1.247462       0.667021  0.398862   0.431331   1.802776              -0.253961      0.0  ... -1.416757   -1.779882 -1.906925                 -0.175065  -0.906327       -0.948122   0.551472
50     -0.589747       5.869600  0.398862   0.431331  -0.554700               0.380210      0.0  ... -0.126496    0.097100  0.524404                 -0.226837  -0.906327       -0.948122   0.551472

[51 rows x 22 columns], 0     0
1     3
2     1
3     1
4     0
5     2
6     1
7     1
8     0
9     1
10    2
11    2
12    1
13    2
14    0
15    1
16    1
17    1
18    1
19    0
20    1
21    0
22    1
23    1
24    2
25    1
26    1
27    1
28    1
29    1
30    1
31    1
32    2
33    1
34    1
35    1
36    1
37    2
38    1
39    2
40    2
41    1
42    1
43    1
44    1
45    1
46    1
47    1
48    0
49    1
50    1
Name: Target_Multiclass, dtype: int32)
val_data : (    Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
51      0.538653       1.023673 -2.507133  -2.318405  -0.554700               3.998712      0.0  ...  0.196069    0.495107  0.524404                 -0.340307  -0.906327       -0.948122  -1.055675
52      0.110985      -0.413030  0.398862  -2.318405   1.802776              -0.910514      0.0  ... -1.416757   -0.804153 -1.906925                 -0.235417  -0.906327       -0.948122  -0.252101
53      0.380848      -0.518343  0.398862  -2.318405  -0.554700              -3.588952      0.0  ... -0.126496    0.466058 -1.906925                 -0.267645  -0.906327       -0.948122   1.355045
54     -0.290646      -0.232012  0.398862   0.431331  -0.554700               0.357827      0.0  ...  1.486330    0.625389  0.524404                 -0.095311   1.103355        0.911656   1.355045
55     -0.834217       0.019832  0.398862   0.431331  -0.554700               0.305601      0.0  ...  0.196069    1.168410  0.524404                 -0.225883  -0.906327       -0.948122  -1.055675
56      0.789002       0.640272  0.398862  -2.318405  -0.554700              -2.656348      0.0  ... -0.126496   -0.269449 -1.906925                 -0.159741  -0.906327       -0.948122   0.551472
57      0.989184       1.001466  0.398862  -2.318405  -0.554700              -2.439984      0.0  ...  1.163765   -0.462105 -1.906925                 -0.199892   1.103355       -0.948122   0.551472
58      0.622002       0.303976  0.398862  -2.318405  -0.554700              -6.088330      0.0  ...  0.196069    1.105953 -1.906925                 -0.370438  -0.906327       -0.948122   1.355045
59      1.194197       0.458413  0.398862   0.431331  -0.554700              -1.179104      0.0  ...  0.196069    0.877671  0.524404                 -0.078435  -0.906327        0.911656  -1.055675
60     -0.383176       1.477395  0.398862   0.431331  -0.554700               0.507044      0.0  ... -0.449061   -0.610365  0.524404                 -0.088553  -0.906327        0.911656  -0.252101
61      2.148924      -0.192477  0.398862   0.431331  -0.554700               1.126293      0.0  ...  0.518634    3.370892  0.524404                  0.590291   1.103355        0.911656   0.551472
62      0.060543      -0.389814  0.398862   0.431331   1.802776              -1.358164      0.0  ... -1.416757   -1.144659  0.524404                 -0.205934  -0.906327       -0.948122   1.355045
63     -0.645804       0.070301  0.398862   0.431331   1.802776              -0.925435      0.0  ... -1.416757   -0.460544  0.524404                 -0.201253  -0.906327       -0.948122   0.551472
64      0.821402       0.173428  0.398862   0.431331  -0.554700              -0.201735      0.0  ...  0.196069    0.502206 -1.906925                 -0.152893  -0.906327       -0.948122  -0.252101
65     -0.172822      -0.224273  0.398862   0.431331  -0.554700               1.185979      0.0  ...  1.486330   -0.065709  0.524404                  0.086356   1.103355        0.911656  -1.055675
66     -0.045811       0.682834  0.398862   0.431331  -0.554700              -0.709071      0.0  ...  0.518634    0.857685  0.524404                  0.027395   1.103355        0.911656  -1.859248
67      0.993115       0.178811  0.398862   0.431331  -0.554700              -2.014717      0.0  ...  0.841200    1.652658  0.524404                  0.280601   1.103355        0.911656   1.355045

[17 rows x 22 columns], 51    1
52    1
53    1
54    1
55    1
56    2
57    1
58    1
59    1
60    1
61    1
62    2
63    2
64    1
65    0
66    0
67    1
Name: Target_Multiclass, dtype: int32)
test_data : (    Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
68      0.807711      -0.164719  0.398862   0.431331   1.802776              -0.955279      0.0  ... -1.739322    0.552153  0.524404                 -0.069601  -0.906327       -0.948122  -1.055675
69      1.594785       1.503134  0.398862   0.431331  -0.554700               0.969615      0.0  ...  0.196069   -1.208742 -1.906925                 -0.308989  -0.906327       -0.948122  -1.859248
70      1.606493       0.125818  0.398862   0.431331   1.802776              -1.276094      0.0  ... -1.416757   -0.509558  0.524404                 -0.080869  -0.906327        0.911656  -0.252101
71      0.579109       0.014953 -2.507133  -2.318405   1.802776               3.334698      0.0  ... -1.739322   -0.347250 -1.906925                 -0.290197  -0.906327       -0.948122  -1.055675
72      2.220262       0.020505 -2.507133   0.431331  -0.554700               0.939772      0.0  ... -0.126496    0.551730 -1.906925                 -0.239646  -0.906327       -0.948122  -1.055675
73      0.056283       0.080059  0.398862   0.431331  -0.554700              -2.342993      0.0  ...  0.196069    0.879510  0.524404                  0.390382  -0.906327        0.911656  -1.055675
74      0.096960       0.537818  0.398862   0.431331  -0.554700              -1.059730      0.0  ...  0.196069    1.087519  0.524404                 -0.226195  -0.906327       -0.948122   1.355045
75      1.335591       1.441225  0.398862  -2.318405  -0.554700              -1.059730      0.0  ...  0.196069   -4.222525 -1.906925                 -0.232732  -0.906327       -0.948122  -1.859248
76      0.542848      -0.247489  0.398862   0.431331  -0.554700               2.267800      0.0  ...  1.163765    0.405797  0.524404                  0.117129   1.103355        0.911656   0.551472
77     -0.272977       0.873441  0.398862   0.431331  -0.554700               0.193689      0.0  ... -0.126496   -0.211747  0.524404                 -0.210963  -0.906327       -0.948122   1.355045
78     -0.387555      -0.352130  0.398862  -2.318405  -0.554700              -0.955279      0.0  ...  1.163765   -0.471589 -1.906925                 -0.031539   1.103355       -0.948122  -1.859248
79      0.131219      -0.596235  0.398862   0.431331  -0.554700              -2.231081      0.0  ...  1.486330   -0.010852  0.524404                  0.035987   1.103355        0.911656  -1.055675
80      0.654083      -0.076565  0.398862  -2.318405  -0.554700              -4.916979      0.0  ... -3.352148    0.653388  0.524404                 -0.046995  -0.906327       -0.948122  -1.859248
81     -0.106047       0.355623  0.398862   0.431331  -0.554700              -0.470325      0.0  ...  0.841200    0.753621  0.524404                 -0.014117   1.103355        0.911656   0.551472
82      1.286579       0.503499  0.398862   0.431331  -0.554700               2.700528      0.0  ...  1.163765   -1.283010  0.524404                  1.866347   1.103355        0.911656   0.551472
83      0.212934       1.477731  0.398862   0.431331  -0.554700             -13.996809      0.0  ...  0.196069    1.221443  0.524404                  0.343983  -0.906327        0.911656  -0.252101
84      0.029370       0.661133 -2.507133   0.431331  -0.554700               5.684860      0.0  ...  0.841200   -0.058833  0.524404                 -3.606227   1.103355       -0.948122   1.355045

[17 rows x 22 columns], 68    1
69    1
70    1
71    1
72    1
73    1
74    2
75    1
76    2
77    0
78    1
79    1
80    1
81    1
82    1
83    2
84    2
Name: Target_Multiclass, dtype: int32)
df :              Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0      2013.07.18  12:00:00  1281.04  1282.29  1278.50  1280.54    3360  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
1      2013.07.18  13:00:00  1280.53  1282.77  1280.18  1280.85    2748  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
2      2013.07.18  14:00:00  1281.10  1282.35  1280.29  1281.04    2718  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
3      2013.07.18  15:00:00  1281.14  1287.04  1277.57  1285.03    4864  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
4      2013.07.18  16:00:00  1285.12  1288.40  1283.80  1286.97    4524  ...               0.0              0.0                -1.0                      0.0           0.0           0.0             0.0
...           ...       ...      ...      ...      ...      ...     ...  ...               ...              ...                 ...                      ...           ...           ...             ...
71823  2025.07.11  19:00:00  3358.83  3359.63  3352.79  3353.84   11242  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71824  2025.07.11  20:00:00  3353.65  3354.09  3349.24  3353.38    9305  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71825  2025.07.11  21:00:00  3353.37  3358.48  3353.32  3356.09    7062  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71826  2025.07.11  22:00:00  3356.09  3359.72  3355.59  3356.40    7058  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0
71827  2025.07.11  23:00:00  3356.47  3358.82  3353.52  3354.98    3542  ...              -1.0              0.0                -1.0                     -1.0          -1.0          -1.0            -1.0

[71828 rows x 332 columns]
trade_df :             Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour  ...  D1_MACD_deep  D1_MACD_signal  RR_Ratio  Target  Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0  2013-09-10 10:00:00      1378.60 2013-09-10 12:00:00     1373.07       Sell   553.0          1          10  ...          -1.0            -1.0       inf       1            1          -1            1                  0
1  2013-10-29 11:00:00      1344.49 2013-10-29 11:00:00     1344.49        Buy     0.0          1          11  ...           1.0             1.0       inf       0            0           0           -1                  3
2  2013-11-06 14:00:00      1318.39 2013-11-06 17:00:00     1318.39       Sell     0.0          2          14  ...          -1.0            -1.0       inf       0            0          -1            0                  1
3  2013-11-14 19:00:00      1289.44 2013-11-15 03:00:00     1289.44       Sell     0.0          3          19  ...          -1.0            -1.0       inf       0            0          -1            0                  1
4  2013-11-21 15:00:00      1247.99 2013-11-21 15:00:00     1240.49       Sell   750.0          3          15  ...          -1.0            -1.0  3.000000       1            1          -1            1                  0
..                 ...          ...                 ...         ...        ...     ...        ...         ...  ...           ...             ...       ...     ...          ...         ...          ...                ...
80 2024-07-29 05:00:00      2392.71 2024-07-29 12:00:00     2392.71       Sell     0.0          0           5  ...          -1.0            -1.0       inf       0            0          -1            0                  1
81 2024-10-10 18:00:00      2625.42 2024-10-10 18:00:00     2625.42       Sell     0.0          3          18  ...          -1.0            -1.0       inf       0            0          -1            0                  1
82 2024-11-07 19:00:00      2695.81 2024-11-07 19:00:00     2695.81       Sell     0.0          3          19  ...          -1.0            -1.0       inf       0            0          -1            0                  1
83 2025-04-09 16:00:00      3047.37 2025-04-09 16:00:00     3073.23       Sell -2586.0          2          16  ...          -1.0            -1.0  3.000387       0            0          -1            0                  2
84 2025-05-16 18:00:00      3173.21 2025-05-16 19:00:00     3188.32       Sell -1511.0          4          18  ...          -1.0            -1.0  3.000662       0            0          -1            0                  2

[85 rows x 338 columns]
stats : {'buy': {'win_rate': 0, 'expectancy': 0.0}, 'sell': {'win_rate': 38.46, 'expectancy': -67.5}, 'buy_sell': {'win_rate': 38.46, 'expectancy': -67.5}}
features : 22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-09-10 ถึง 2021-07-01 (2852 วัน, 51 records)
Val: 2021-08-10 ถึง 2022-10-21 (438 วัน, 17 records)
Test: 2022-11-01 ถึง 2025-05-16 (928 วัน, 17 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 332

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              10        11.76%
SL Hit              75        88.24%
Technical Exit      0         0.00%
SL + Tech Exit      75        88.24%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 805.50
ขาดทุนเฉลี่ยเมื่อ SL Hit: -130.80
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:6.16

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 75
- อัตราส่วน: 88.24%
- กำไร/ขาดทุนเฉลี่ย: -130.80
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:6.16

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                        Target
           count        mean     sum      mean
DayOfWeek                                     
Monday         8  241.625000  1933.0  0.250000
Tuesday       23   28.478261   655.0  0.086957
Wednesday     14  -92.428571 -1294.0  0.071429
Thursday      21  -22.190476  -466.0  0.142857
Friday        19 -135.947368 -2583.0  0.105263

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count        mean     sum      mean
Hour                                     
4         1    0.000000     0.0  0.000000
9         7  219.142857  1534.0  0.428571
10        8  -12.750000  -102.0  0.125000
11        3  -98.000000  -294.0  0.000000
12        4 -140.000000  -560.0  0.000000
13        2    0.000000     0.0  0.000000
14       10  -48.000000  -480.0  0.200000
15       15 -216.066667 -3241.0  0.000000
16        8  137.000000  1096.0  0.125000
17       12   36.000000   432.0  0.166667
18        9 -107.444444  -967.0  0.000000
19        6  137.833333   827.0  0.166667
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_GOLD_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (71828, 332), trade_df.shape = (85, 338)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_09a_combined_df.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 85 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_09b_combined_df_merge.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 342
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon']
✅ ใช้ Final selected feature: 22 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ GOLD_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 35864 (49.9%)
  downtrend: 30186 (42.0%)
  sideways: 5778 (8.0%)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_10a_df_with_scenario.csv

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66050/71828 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66050 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 65993, 1.0: 48, 2.0: 8, 3.0: 1}
⚠️ พบ class ที่มีข้อมูลน้อยเกินไป (ต่ำสุด: 1 samples)
🔧 อาจต้องปรับการแบ่งข้อมูลในขั้นตอนการเทรน
✅ เตรียมข้อมูล trend_following: 66050 samples, 22 features
✅ ข้อมูลพร้อม: X.shape=(66050, 22), y.shape=(66050,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ GOLD_M60
📊 ข้อมูล: 66050 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 65993, 1.0: 48, 2.0: 8, 3.0: 1}
⚠️ พบ class ที่มีข้อมูลน้อยเกินไป (ต่ำสุด: 1 samples)
🔧 จะใช้การแบ่งข้อมูลแบบไม่ stratify
📈 Train: 39630, Val: 13210, Test: 13210
📊 Train class distribution: {0.0: 39592, 1.0: 33, 2.0: 4, 3.0: 1}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=1, nan=0, large_values=1
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=2, nan=0, large_values=2
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.2502399474641342, 1.0: 300.22727272727275, 2.0: 2476.875, 3.0: 9907.5}
📊 trend_following Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.037 - 0.087 (base: 0.062)
   num_leaves: 21 - 27 (base: 24)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 12, 'max_depth': 7, 'learning_rate': 0.03, 'feature_fraction': 0.82, 'bagging_freq': 1, 'bagging_fraction': 0.87}
✅ Best CV score สำหรับ trend_following: 0.6901

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
42397      1.129412         3346.0       1.0        0.0          0                   0.39        0  ...    13    0.027027     0.0                  3.188388          0            -1.0          0
20272     -1.609929         2067.0       1.0        0.0          0                   1.34        0  ...    19   -0.022023     1.0                 32.721201          1             0.0          3
17011     -2.357143         7503.0       1.0        0.0          0                  -0.01        0  ...     5   -0.053323    -1.0                 -1.696988          0             1.0          2
53909      0.470067         2904.0       1.0       -1.0          0                   7.99        0  ...    17    0.219512    -1.0                -18.940657          1             0.0          3
59239     -0.430769         6560.0      -1.0        1.0          0                  -1.95        1  ...     1   -0.075099     1.0                 -2.923354          0             0.0          4

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following
📊 Class distribution: {0.0: 39592, 1.0: 33, 2.0: 4, 3.0: 1}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 39592, 1.0: 33, 2.0: 4, 3.0: 1}
🔄 Oversampling class 1.0: 33 -> 200 (+167 samples)
🔄 Oversampling class 2.0: 4 -> 200 (+196 samples)
🔄 Oversampling class 3.0: 1 -> 200 (+199 samples)
📊 Class distribution หลัง oversample: {0.0: 39592, 1.0: 200, 3.0: 200, 2.0: 200}
📊 Class distribution หลัง oversample: {0.0: 39592, 1.0: 200, 2.0: 200, 3.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11a_df_XTrain_trend_following.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11b_df_YTrain_trend_following.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's multi_logloss: 0.033057

Accuracy: 0.9933
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      0.99      1.00     13199
         1.0       0.01      0.10      0.02        10
         2.0       0.00      0.00      0.00         1

    accuracy                           0.99     13210
   macro avg       0.34      0.36      0.34     13210
weighted avg       1.00      0.99      1.00     13210

Confusion Matrix:
[[13121    76     2]
 [    9     1     0]
 [    1     0     0]]
📊 Test Set Accuracy: 0.9933, F1-Score: 0.9958
🔍 ประเมินผลแบบ Multiclass
⚠️ ไม่สามารถคำนวณ Multiclass AUC: Number of classes in y_true not equal to the number of columns in 'y_score', ใช้ accuracy แทน
✅ trend_following - Accuracy: 0.993, F1: 0.996, AUC: 0.993

🔍 ตรวจสอบคุณภาพโมเดล trend_following...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13210, 22)
   y_val shape: (13210,), unique: [0. 1. 2.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13210, 22)
   y_val shape: (13210,), unique: [0. 1. 2.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13210, 4)
   Multi-class classification detected (4 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (13210,), unique: [0. 1. 2.], type: <class 'numpy.ndarray'>
⚠️ ไม่สามารถคำนวณ AUC ได้: Number of classes in y_true not equal to the number of columns in 'y_score'
🔍 Debug Metrics Calculation:
   y_val shape: (13210,), unique values: [0. 1. 2.]
   y_pred shape: (13210,), unique values: [0. 1. 2.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Calculated metrics: Acc=0.9942, F1=0.9965, Prec=0.9989, Rec=0.9942
✅ ML Metrics:
   Accuracy: 0.9942
   AUC: 0.9942
   F1: 0.9965
   Precision: 0.9989
   Recall: 0.9942
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.72
   Trades: 1321

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD MM60 (trend_following) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 16:01:47
📊 โมเดล: GOLD MM60 (trend_following)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
🏷️ ประเภท: declined
================================================================================
⏭️ ไม่บันทึกโมเดล trend_following - โมเดลไม่ดีขึ้น - ไม่มีการปรับปรุงที่สำคัญ
📊 สร้าง Feature Importance สำหรับ trend_following
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_GOLD_M60 symbol GOLD timeframe M60 (trend_following)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
Momentum5_x_Volatility10 0.2849 0.1146
         Volume_Change_1 0.1655 0.0830
           Volume_Lag_10 0.0867 0.0771
           Volume_Lag_20 0.0828 0.0719
              ATR_ROC_i2 0.0782 0.0524
            Volume_Lag_5 0.0494 0.0665
                  Bar_SW 0.0479 0.0395
             Price_Range 0.0417 0.0565
                    Hour 0.0370 0.0541
            Bar_longwick 0.0270 0.0492
   MACD_line_x_PriceMove 0.0190 0.0805
          H12_Price_Move 0.0124 0.0497
         H12_Price_Range 0.0112 0.0502
           Volume_Lag_30 0.0106 0.0454
                 IsNight 0.0095 0.0113

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_feature_importance.csv (ขนาด: 1223 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
           Volume_Lag_10    0.152466
Momentum5_x_Volatility10    0.113206
              ATR_ROC_i2    0.111517
            Volume_Lag_5    0.086476
            Bar_longwick    0.075459
          H12_Price_Move    0.063806
   MACD_line_x_PriceMove    0.062780
           Volume_Lag_30    0.057576
             Price_Range    0.053530
         H12_Price_Range    0.050186
         Volume_Change_1    0.046787
           Volume_Lag_20    0.042454
          Price_Strangth    0.021454
                    Hour    0.015085
             IsAfternoon    0.012139

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13199
         1.0       0.00      0.00      0.00        10
         2.0       0.00      0.00      0.00         1

    accuracy                           1.00     13210
   macro avg       0.33      0.33      0.33     13210
weighted avg       1.00      1.00      1.00     13210

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following
✅ สร้าง trade_df สำหรับ trend_following: 13210 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 344 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 272162 bytes
✅ เทรน trend_following สำเร็จ

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71828 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71828 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5760, 1.0: 10, 2.0: 8}
✅ เตรียมข้อมูล counter_trend: 5778 samples, 22 features
✅ ข้อมูลพร้อม: X.shape=(5778, 22), y.shape=(5778,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5760, 1.0: 10, 2.0: 8}
📈 Train: 3466, Val: 1156, Test: 1156
📊 Train class distribution: {0.0: 3456, 1.0: 6, 2.0: 4}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=0
📊 X_val: inf=0, nan=0, large_values=0
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_counter_trend_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0618, leaves=24 (Symbol: GOLD, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.33429783950617287, 1.0: 192.55555555555554, 2.0: 288.8333333333333}
📊 counter_trend Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.022 - 0.102 (base: 0.062)
   num_leaves: 14 - 34 (base: 24)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend: {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}
✅ Best CV score สำหรับ counter_trend: 0.8563

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
5064       1.125000         1873.0      -1.0        1.0          0                  -0.56        0  ...     6   -0.537725     1.0                  0.529383          0            -1.0          4
23784     -0.264286         8961.0      -1.0        1.0          1                  -1.27        0  ...    10   -0.056245     1.0                  3.169503          0             0.0          4
40911      0.586207         2770.0       1.0        1.0          0                  -3.73        0  ...    14    0.105504     1.0                 20.650020          0             0.0          0
59601     -0.087065         1465.0      -1.0        1.0          0                   3.95        1  ...    20    0.005680     1.0                -12.222407          0             0.0          4
21621      0.356436         9437.0       1.0        1.0          0                  -0.69        0  ...    13   -0.036508     1.0                  2.228532          0             0.0          2

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend
📊 Class distribution: {0.0: 3456, 1.0: 6, 2.0: 4}
📊 Imbalance ratio: 0.001
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 3456, 1.0: 6, 2.0: 4}
🔄 Oversampling class 1.0: 6 -> 200 (+194 samples)
🔄 Oversampling class 2.0: 4 -> 200 (+196 samples)
📊 Class distribution หลัง oversample: {0.0: 3456, 2.0: 200, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 3456, 1.0: 200, 2.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11a_df_XTrain_counter_trend.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11b_df_YTrain_counter_trend.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[55]	valid_0's multi_logloss: 0.0208959

Accuracy: 0.9948
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1152
         1.0       0.00      0.00      0.00         2
         2.0       0.00      0.00      0.00         2

    accuracy                           0.99      1156
   macro avg       0.33      0.33      0.33      1156
weighted avg       0.99      0.99      0.99      1156

Confusion Matrix:
[[1150    0    2]
 [   2    0    0]
 [   2    0    0]]
📊 Test Set Accuracy: 0.9948, F1-Score: 0.9939
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.6850
✅ counter_trend - Accuracy: 0.995, F1: 0.994, AUC: 0.685

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1156, 22)
   y_val shape: (1156,), unique: [0. 1. 2.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (counter_trend)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1156, 22)
   y_val shape: (1156,), unique: [0. 1. 2.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1156, 3)
   Multi-class classification detected (3 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (1156,), unique: [0. 2.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1156,), unique values: [0. 1. 2.]
   y_pred shape: (1156,), unique values: [0. 2.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Calculated metrics: Acc=0.9974, F1=0.9964, Prec=0.9957, Rec=0.9974
✅ ML Metrics:
   Accuracy: 0.9974
   AUC: 0.9064
   F1: 0.9964
   Precision: 0.9957
   Recall: 0.9974
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.72
   Trades: 115

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล GOLD MM60 (counter_trend) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน save current model metrics
✅ บันทึก metrics ปัจจุบันสำเร็จ: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน send model alert

================================================================================
✅ MODEL ALERT - SUCCESS
================================================================================
🕒 เวลา: 2025-09-14 16:02:00
📊 โมเดล: GOLD MM60 (counter_trend)
📋 รายละเอียด: โมเดลผ่านการประเมิน - โมเดลดีขึ้น - auc: +0.0105 (+1.17%)
💾 การบันทึก: ✅ บันทึกแล้ว
📝 เหตุผล: โมเดลดีขึ้น - auc: +0.0105 (+1.17%)
🏷️ ประเภท: improved
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (counter_trend)
================================================================================
💾 บันทึกโมเดล: ✅ ใช่
📝 เหตุผล: โมเดลดีขึ้น - auc: +0.0105 (+1.17%)
🏷️ ประเภท: improved
================================================================================
✅ บันทึกโมเดล counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_trained.pkl
✅ บันทึก features counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_features.pkl
✅ บันทึก scaler counter_trend ที่: LightGBM/Multi/models\counter_trend\M60_GOLD_scaler.pkl
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_GOLD_M60 symbol GOLD timeframe M60 (counter_trend)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
            Bar_longwick 0.2132 0.1039
          H12_Price_Move 0.1877 0.0964
               Bar_CL_HL 0.1660 0.0391
Momentum5_x_Volatility10 0.0648 0.0910
           Volume_Lag_30 0.0638 0.0877
           Volume_Lag_10 0.0575 0.0628
   MACD_line_x_PriceMove 0.0548 0.0989
         Volume_Change_1 0.0439 0.0881
            Volume_Lag_5 0.0328 0.0424
               DayOfWeek 0.0303 0.0262
             Price_Range 0.0186 0.0578
                STO_zone 0.0168 0.0100
           Volume_Lag_20 0.0142 0.0482
                 IsNight 0.0101 0.0150
              ATR_ROC_i2 0.0101 0.0461

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_counter_trend_feature_importance.csv (ขนาด: 1226 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
          H12_Price_Move    0.176940
   MACD_line_x_PriceMove    0.097633
            Bar_longwick    0.090048
         H12_Price_Range    0.081523
             Price_Range    0.061564
           Volume_Lag_10    0.059677
Momentum5_x_Volatility10    0.053922
         Volume_Change_1    0.051119
           Volume_Lag_30    0.050355
              ATR_ROC_i2    0.047906
               DayOfWeek    0.045820
            Volume_Lag_5    0.038689
           Volume_Lag_20    0.028678
               Bar_CL_HL    0.028363
                    Hour    0.024173

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1152
         1.0       0.00      0.00      0.00         2
         2.0       0.00      0.00      0.00         2

    accuracy                           1.00      1156
   macro avg       0.33      0.33      0.33      1156
weighted avg       0.99      1.00      0.99      1156

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 1156 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 335 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 270856 bytes
✅ เทรน counter_trend สำเร็จ
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_10b_df_with_scenario.csv

📊 กำลังเทรน Target_Buy...

--- Training for Target_Buy ---

📊 กำลังเทรน trend_following สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71744 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 65984/71744 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 65984 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 65984}
✅ เตรียมข้อมูล trend_following: 65984 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 65984 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 65984 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following_Buy ล้มเหลว - result เป็น None

📊 กำลังเทรน counter_trend สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71744 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5760/71744 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5760 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5760}
✅ เตรียมข้อมูล counter_trend: 5760 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5760 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ GOLD_M60
📊 ข้อมูล: 5760 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Buy ล้มเหลว - result เป็น None

📊 กำลังเทรน Target_Sell...

--- Training for Target_Sell ---

📊 กำลังเทรน trend_following สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 71827 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 66049/71827 rows (92.0%)
📊 ข้อมูลหลังกรอง trend_following: 66049 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 66039, 1.0: 10}
✅ เตรียมข้อมูล trend_following: 66049 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 66049 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 66049 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 66039, 1.0: 10}
📈 Train: 39629, Val: 13210, Test: 13210
📊 Train class distribution: {0.0: 39623, 1.0: 6}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=2, nan=0, large_values=2
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=1, nan=0, large_values=1
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_GOLD\M60_GOLD_trend_following_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following_Sell
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 39623, 1.0: 6}
   Imbalance ratio: 6603.8:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 trend_following_Sell Parameter Distributions (based on GOLD_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following_Sell: {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 10, 'max_depth': 8, 'learning_rate': 0.05, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.84}
✅ Best CV score สำหรับ trend_following_Sell: 0.9262

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
32360      0.521552         3907.0       1.0        0.0          0                  -2.32        0  ...    17    0.110563     0.0                 29.612330          1             1.0          3
47584      1.435185        36478.0      -1.0        0.0          0                  -0.31        0  ...    12    0.089245     0.0                 -3.289823          0             0.0          0
47983      0.204082         1807.0       1.0        0.0          0                  -2.75        1  ...    20    0.110767     0.0                 70.418623          0             1.0          2
18601      0.375000         1583.0      -1.0        0.0          0                   0.78        1  ...    21    0.065519     0.0                -23.239345          0             0.0          3
69760      0.492386         6033.0       1.0        0.0          0                  -2.64        0  ...    18    0.048120     0.0                 83.515400          1             0.0          3

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Sell
📊 Class distribution: {0.0: 39623, 1.0: 6}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 39623, 1.0: 6}
🔄 Oversampling class 1.0: 6 -> 200 (+194 samples)
📊 Class distribution หลัง oversample: {0.0: 39623, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 39623, 1.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11a_df_XTrain_trend_following_Sell.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_GOLD_11b_df_YTrain_trend_following_Sell.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.00542041

Accuracy: 0.9994
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13208
         1.0       0.00      0.00      0.00         2

    accuracy                           1.00     13210
   macro avg       0.50      0.50      0.50     13210
weighted avg       1.00      1.00      1.00     13210

Confusion Matrix:
[[13202     6]
 [    2     0]]
📊 Test Set Accuracy: 0.9994, F1-Score: 0.9995
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.5196
✅ trend_following_Sell - Accuracy: 0.999, F1: 1.000, AUC: 0.520

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13210, 22)
   y_val shape: (13210,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: GOLD MM60 (trend_following_Sell)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13210, 22)
   y_val shape: (13210,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13210, 2)
   Binary classification detected
   Final y_pred shape: (13210,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (13210,), unique values: [0. 1.]
   y_pred shape: (13210,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9996, F1=0.0000, Prec=0.0000, Rec=0.0000
✅ ML Metrics:
   Accuracy: 0.9996
   AUC: 0.8004
   F1: 0.0000
   Precision: 0.0000
   Recall: 0.0000
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.97
   Trades: 1321

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล GOLD MM60 (trend_following_Sell) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - F1 Score 0.0000 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0000 < 0.1
   - Recall 0.0000 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_GOLD_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 16:02:37
📊 โมเดล: GOLD MM60 (trend_following_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: GOLD MM60 (trend_following_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล trend_following_Sell - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
📊 สร้าง Feature Importance สำหรับ trend_following_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Sell_GOLD_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Sell_GOLD_M60 symbol GOLD timeframe M60 (trend_following_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
           Volume_Lag_10 0.2943 0.0980
            Bar_longwick 0.2327 0.1784
         Volume_Change_1 0.1369 0.1107
Momentum5_x_Volatility10 0.1295 0.0793
          H12_Price_Move 0.0520 0.0765
                  Bar_SW 0.0241 0.0760
         H12_Price_Range 0.0234 0.0396
           Volume_Lag_30 0.0230 0.0699
             Price_Range 0.0229 0.0545
              ATR_ROC_i2 0.0151 0.0622
   MACD_line_x_PriceMove 0.0131 0.0385
               IsEvening 0.0103 0.0061
           Volume_Lag_20 0.0090 0.0308
                    Hour 0.0042 0.0165
            Volume_Lag_5 0.0031 0.0149

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_GOLD_trend_following_Sell_feature_importance.csv (ขนาด: 1201 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
           Volume_Lag_10    0.115402
Momentum5_x_Volatility10    0.111791
           Volume_Lag_20    0.099001
            Bar_longwick    0.085756
           Volume_Lag_30    0.085625
         H12_Price_Range    0.068206
            Volume_Lag_5    0.067738
             Price_Range    0.061088
         Volume_Change_1    0.057032
          H12_Price_Move    0.055868
              ATR_ROC_i2    0.050185
   MACD_line_x_PriceMove    0.041112
                    Hour    0.028760
          Price_Strangth    0.028295
               DayOfWeek    0.017278

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13208
         1.0       0.00      0.00      0.00         2

    accuracy                           1.00     13210
   macro avg       0.50      0.50      0.50     13210
weighted avg       1.00      1.00      1.00     13210

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_GOLD_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง trade_df สำหรับ trend_following_Sell: 13210 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Sell/M60_GOLD
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_evaluation_report.csv
📁 ขนาดไฟล์: 359 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Sell/M60_GOLD\M60_GOLD_performance_curves.png
📁 ขนาดไฟล์: 273202 bytes
✅ เทรน trend_following_Sell สำเร็จ

📊 กำลังเทรน counter_trend สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 71827 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5778/71827 rows (8.0%)
📊 ข้อมูลหลังกรอง counter_trend: 5778 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5778}
✅ เตรียมข้อมูล counter_trend: 5778 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ GOLD_M60
📊 ข้อมูล: 5778 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Sell ล้มเหลว - result เป็น None

✅ เทรนเสร็จสิ้น: 3 โมเดล
🔍 Debug: ผลลัพธ์การเทรน:
  ✅ trend_following: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Sell: มีผลลัพธ์
    📊 Feature Importance: True

📊 บันทึก Random Forest Feature Importance

🏗️ เปิดใช้งาน save combined random forest importance
📊 รวบรวม RF Feature Importance จาก trend_following
📊 รวบรวม RF Feature Importance จาก counter_trend
📊 รวบรวม RF Feature Importance จาก trend_following_Sell
🔍 Debug: คอลัมน์ใน combined_rf_df: ['Feature', 'Importance', 'Scenario']
💾 บันทึก Random Forest Feature Importance: LightGBM/Multi/results\M60_GOLD_random_forest_feature_importance.csv
📊 รวมจาก 3 scenarios, 22 features

🏆 Top 10 Random Forest Features:
  Volume_Lag_10: Importance=0.1092
  H12_Price_Move: Importance=0.0989
  Momentum5_x_Volatility10: Importance=0.0930
  Bar_longwick: Importance=0.0838
  ATR_ROC_i2: Importance=0.0699
  MACD_line_x_PriceMove: Importance=0.0672
  H12_Price_Range: Importance=0.0666
  Volume_Lag_30: Importance=0.0645
  Volume_Lag_5: Importance=0.0643
  Price_Range: Importance=0.0587

📊 สร้าง Combined Feature Importance จากทุก scenarios

🏗️ เปิดใช้งาน create combined feature importance
📊 รวบรวม Feature Importance จาก trend_following
📊 รวบรวม Feature Importance จาก counter_trend
📊 รวบรวม Feature Importance จาก trend_following_Sell
💾 บันทึก Combined Feature Importance: LightGBM/Multi/results/M60\M60_GOLD_feature_importance.csv
📊 รวมจาก 3 scenarios, 22 features

🏆 Top 10 Features (Combined):
  Momentum5_x_Volatility10: Gain=0.1597, Split=0.0950, Count=3
  Bar_longwick: Gain=0.1576, Split=0.1105, Count=3
  Volume_Lag_10: Gain=0.1462, Split=0.0793, Count=3
  Volume_Change_1: Gain=0.1154, Split=0.0939, Count=3
  H12_Price_Move: Gain=0.0840, Split=0.0742, Count=3
  Bar_CL_HL: Gain=0.0590, Split=0.0217, Count=3
  Volume_Lag_20: Gain=0.0353, Split=0.0503, Count=3
  ATR_ROC_i2: Gain=0.0345, Split=0.0536, Count=3
  Volume_Lag_30: Gain=0.0325, Split=0.0677, Count=3
  MACD_line_x_PriceMove: Gain=0.0290, Split=0.0727, Count=3

🔍 ทำ Cross-Validation และ Threshold Optimization สำหรับ Multi-Model
📊 ทำ Time Series Cross-Validation...

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=10261, total_samples=71828

📊 Fold 1/5:
  - Train size: 20523 ตัวอย่าง (28.6% ของข้อมูลทั้งหมด)
  - Val size:   10261 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9992691127028213, 1.0: 0.00048725819811918335, 2.0: 0.00019490327924767334, 3.0: 4.8725819811918335e-05}
⚠️ เกิดข้อผิดพลาด ร้ายแรงใน time series cv: Input X contains infinity or a value too large for dtype('float64').
✅ Time Series CV เสร็จสิ้น: AUC=0.500, F1=0.000
🔍 Debug: กำลังบันทึก CV results ที่ LightGBM/Multi/results\multi_scenario_cv_results.json
💾 บันทึก CV Results: LightGBM/Multi/results\multi_scenario_cv_results.json
📁 ขนาดไฟล์: 83 bytes

📊 สร้าง Performance Analysis และ Comparison Plots

🏗️ เปิดใช้งาน create multi scenario performance analysis
🔍 Debug: กำลังบันทึกไฟล์ที่ LightGBM/Multi/results\multi_scenario_performance_analysis.txt
💾 บันทึก Multi-Scenario Performance Analysis: LightGBM/Multi/results\multi_scenario_performance_analysis.txt
📁 ขนาดไฟล์: 1230 bytes

🏗️ เปิดใช้งาน create performance comparison plots
🔍 Debug: สร้างโฟลเดอร์ plots ที่ LightGBM/Multi/results/plots
🔍 Debug: จำนวน scenarios: 3
🔍 Debug: scenarios: ['trend_following', 'counter_trend', 'trend_following_Sell']
🔍 Debug: metrics data: {'AUC': [0.9933383800151401, 0.6849936732772423, 0.5196093276801939], 'F1_Score': [0.9958452876926704, 0.993946887463725, 0.9995457527793149], 'Accuracy': [0.9933383800151401, 0.9948096885813149, 0.9993943981831945]}
🔍 Debug: สร้างกราฟสำหรับ AUC
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_auc_comparison.png
💾 บันทึก AUC Comparison Plot: LightGBM/Multi/results/plots\performance_auc_comparison.png
📁 ขนาดไฟล์: 126103 bytes
🔍 Debug: สร้างกราฟสำหรับ F1_Score
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_f1_score_comparison.png
💾 บันทึก F1_Score Comparison Plot: LightGBM/Multi/results/plots\performance_f1_score_comparison.png
📁 ขนาดไฟล์: 126092 bytes
🔍 Debug: สร้างกราฟสำหรับ Accuracy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_accuracy_comparison.png
💾 บันทึก Accuracy Comparison Plot: LightGBM/Multi/results/plots\performance_accuracy_comparison.png
📁 ขนาดไฟล์: 129242 bytes
🔍 Debug: สร้างกราฟรวมทุก metrics
🔍 Debug: กำลังบันทึกกราฟรวมที่ LightGBM/Multi/results/plots\performance_combined_comparison.png
💾 บันทึก Combined Performance Comparison Plot: LightGBM/Multi/results/plots\performance_combined_comparison.png
📁 ขนาดไฟล์: 187408 bytes

🏗️ เปิดใช้งาน create final and training results
💾 บันทึก Final Results: LightGBM/Multi/results/trend_following\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/trend_following\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/trend_following\M60_GOLD_feature_importance_comparison.png
💾 บันทึก Final Results: LightGBM/Multi/results/counter_trend\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/counter_trend\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/counter_trend\M60_GOLD_feature_importance_comparison.png

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 3
🔍 Debug: ตรวจสอบ trend_following
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13210
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following
   📊 Test stats: {'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 11.36}, 'sell': {'count': 13208, 'win_rate': 62.2, 'expectancy': 4.92}, 'buy_sell': {'count': 13210, 'win_rate': 62.2, 'expectancy': 4.92}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9933383800151401, 'auc': 0.9933383800151401, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following
🔍 Debug: train_val_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 11.36}, 'sell': {'count': 13208, 'win_rate': 62.2, 'expectancy': 4.92}, 'buy_sell': {'count': 13210, 'win_rate': 62.2, 'expectancy': 4.92}}
🔍 Debug: test_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 11.36}, 'sell': {'count': 13208, 'win_rate': 62.2, 'expectancy': 4.92}, 'buy_sell': {'count': 13210, 'win_rate': 62.2, 'expectancy': 4.92}}
🔍 Debug: model_metrics={'accuracy': 0.9933383800151401, 'auc': 0.9933383800151401, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 78.4/100
📈 Win Rate เฉลี่ย: 63.8%, Expectancy เฉลี่ย: 5.11
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 78.7)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 78.69
✅ บันทึกสรุปสำหรับ trend_following เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1156
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend
   📊 Test stats: {'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 21.7}, 'sell': {'count': 1154, 'win_rate': 62.31, 'expectancy': 4.97}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 5.0}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9948096885813149, 'auc': 0.6849936732772423, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=counter_trend
🔍 Debug: train_val_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 21.7}, 'sell': {'count': 1154, 'win_rate': 62.31, 'expectancy': 4.97}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 5.0}}
🔍 Debug: test_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 21.7}, 'sell': {'count': 1154, 'win_rate': 62.31, 'expectancy': 4.97}, 'buy_sell': {'count': 1156, 'win_rate': 62.37, 'expectancy': 5.0}}
🔍 Debug: model_metrics={'accuracy': 0.9948096885813149, 'auc': 0.6849936732772423, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 76.9/100
📈 Win Rate เฉลี่ย: 63.8%, Expectancy เฉลี่ย: 5.14
🏆 โมเดลที่ดีที่สุด: USDJPY M060 (Score: 78.1)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 75.59
✅ บันทึกสรุปสำหรับ counter_trend เรียบร้อย
🔍 Debug: ตรวจสอบ trend_following_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13210
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}, 'buy_sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9993943981831945, 'auc': 0.5196093276801939, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=GOLD, timeframe=M60, scenario=trend_following_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}, 'buy_sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}, 'buy_sell': {'count': 13210, 'win_rate': 63.09, 'expectancy': 5.03}}
🔍 Debug: model_metrics={'accuracy': 0.9993943981831945, 'auc': 0.5196093276801939, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 77.3/100
📈 Win Rate เฉลี่ย: 64.2%, Expectancy เฉลี่ย: 5.16
🏆 โมเดลที่ดีที่สุด: USDJPY M060 (Score: 78.1)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน GOLD MM60 เรียบร้อย
📊 Performance Score: 76.48
✅ บันทึกสรุปสำหรับ trend_following_Sell เรียบร้อย
📊 บันทึกสรุปเสร็จสิ้น: 3/3 scenarios
✅ เทรนโมเดลสำเร็จ: 3 scenarios
🔍 Debug Multi-Model: จำนวน scenarios = 3
🔍 Debug Scenario 'trend_following': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following' metrics: {'accuracy': 0.9933383800151401, 'f1_score': 0.9958452876926704, 'auc': 0.9933383800151401, 'train_samples': 40192, 'test_samples': 13210, 'scenario': 'trend_following'}
🔍 Debug Scenario 'trend_following' cv_results: {'best_score': 0.6900832702498109, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 12, 'max_depth': 7, 'learning_rate': 0.03, 'feature_fraction': 0.82, 'bagging_freq': 1, 'bagging_fraction': 0.87}, 'scenario': 'trend_following'}
🔍 Debug Scenario 'counter_trend': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend' metrics: {'accuracy': 0.9948096885813149, 'f1_score': 0.993946887463725, 'auc': 0.6849936732772423, 'train_samples': 3856, 'test_samples': 1156, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'counter_trend' cv_results: {'best_score': 0.856319497495968, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'trend_following_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following_Sell' metrics: {'accuracy': 0.9993943981831945, 'f1_score': 0.9995457527793149, 'auc': 0.5196093276801939, 'train_samples': 39823, 'test_samples': 13210, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'trend_following_Sell' cv_results: {'best_score': 0.9262271098466055, 'best_params': {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 10, 'max_depth': 8, 'learning_rate': 0.05, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.84}, 'scenario': 'trend_following_Sell'}
🔍 Debug avg_metrics['accuracy'] = 0.9958 (จาก 3 scenarios)
🔍 Debug avg_metrics['f1_score'] = 0.9964 (จาก 3 scenarios)
🔍 Debug avg_metrics['auc'] = 0.7326 (จาก 3 scenarios)
🔍 Debug avg_metrics['train_samples'] = 27957.0000 (จาก 3 scenarios)
🔍 Debug avg_metrics['test_samples'] = 9192.0000 (จาก 3 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Sell']
🔍 Debug avg_cv_results['best_score'] = 0.8242 (จาก 3 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ best_params: [{'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 12, 'max_depth': 7, 'learning_rate': 0.03, 'feature_fraction': 0.82, 'bagging_freq': 1, 'bagging_fraction': 0.87}, {'reg_lambda': 0.01, 'reg_alpha': 0.0, 'num_leaves': 38, 'min_data_in_leaf': 8, 'max_depth': 8, 'learning_rate': 0.09, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.79}, {'reg_lambda': 0.01, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 10, 'max_depth': 8, 'learning_rate': 0.05, 'feature_fraction': 0.86, 'bagging_freq': 1, 'bagging_fraction': 0.84}]
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['trend_following', 'counter_trend', 'trend_following_Sell']
🔍 Debug Final avg_metrics: {'accuracy': 0.9958474889265498, 'f1_score': 0.9964459759785701, 'auc': 0.7326471269908588, 'train_samples': 27957.0, 'test_samples': 9192.0}
🔍 Debug Final avg_cv_results: {'best_score': 0.8242099591974615}

==================================================
🎯 เริ่มทดสอบ Optimal Parameters
==================================================
🔍 Debug validation data:
   - X_val shape: (17, 22)
   - X_val index range: 51 - 67
   - combined_df shape: (71828, 342)
   - combined_df index range: 0 - 71827
✅ พบ indices ที่ตรงกัน: 17 จาก 17
📊 ข้อมูล validation สำหรับ optimization:
   - จำนวน samples: 17
   - จำนวน features: 22
   - Index range: 51 - 67
⚠️ ข้อมูล validation ไม่เพียงพอ (17 samples)
⚠️ ใช้ fallback method: เพิ่มข้อมูลจาก combined_df
✅ เพิ่มข้อมูล validation เป็น 30 samples
✅ ข้อมูล validation เพียงพอสำหรับการทดสอบ (30 samples)

💾 การบันทึก Artifacts สำหรับการทดสอบแบบ Offline...
✅ บันทึก Models Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_scenario_results.pkl
✅ บันทึก Validation Set Artifact สำเร็จที่: LightGBM/Data_Trained\M60_GOLD_validation_set.csv
⚠️ ไม่พบโมเดล trend_following_Buy: LightGBM/Multi/models/trend_following_Buy/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล counter_trend_Buy: LightGBM/Multi/models/counter_trend_Buy/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล trend_following_Sell: LightGBM/Multi/models/trend_following_Sell/M60_GOLD_trained.pkl
⚠️ ไม่พบโมเดล counter_trend_Sell: LightGBM/Multi/models/counter_trend_Sell/M60_GOLD_trained.pkl
📋 จะทดสอบ 2 scenarios: ['trend_following', 'counter_trend']

📌 Running scenario: trend_following
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_trend_following_optimal_threshold.pkl

📌 Running scenario: counter_trend
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_GOLD_counter_trend_optimal_threshold.pkl

🔍 ทดสอบการเรียกใช้งาน threshold
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
   Threshold  Precision  Recall   F1
0   0.002066        0.0     0.0  0.0
1   0.002354        0.0     0.0  0.0
2   0.002643        0.0     0.0  0.0
3   0.002931        0.0     0.0  0.0
4   0.003219        0.0     0.0  0.0
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
   Threshold  Precision  Recall   F1
0   0.000385        0.0     0.0  0.0
1   0.000452        0.0     0.0  0.0
2   0.000519        0.0     0.0  0.0
3   0.000587        0.0     0.0  0.0
4   0.000654        0.0     0.0  0.0

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
threshold (backward compatibility) : threshold 0.65 default 0.3

✅ symbol GOLD M60 : threshold 0.65 : trend 0.6500 counter 0.6500

🎯 ทดสอบ Optimal nBars SL...
🏗️ เปิดใช้งาน find optimal nbars sl multi model

======================================================================
🎯 เริ่มการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
======================================================================
📊 ข้อมูล validation: 30 samples
🔧 จำนวน scenarios: 3
🎯 nBars_SL เริ่มต้น: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 2
📋 nBars_SL เดิม: 2
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        1.000    0.800      8.00         5.0        🏆 BEST    
3        1.000    0.800      8.00         5.0                  
4        1.000    0.800      8.00         5.0                  
5        1.000    0.800      8.00         5.0                  
6        1.000    0.800      8.00         5.0                  
7        1.000    0.800      8.00         5.0                  
8        1.000    0.800      8.00         5.0                  
9        1.000    0.800      8.00         5.0                  
10       1.000    0.800      8.00         5.0                  
11       1.000    0.800      8.00         5.0                  
12       1.000    0.800      8.00         5.0                  
13       1.000    0.800      8.00         5.0                  
14       1.000    0.800      8.00         5.0                  
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 2
   🔸 nBars_SL ใหม่: 2
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 1.0000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Trend Following: nBars_SL = 2 เหมาะสำหรับการติดตาม trend
   📊 SL ใกล้ - เหมาะกับ volatile market, ป้องกันเร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following: 2
📁 ไฟล์: M60_GOLD_trend_following_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following: 2

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 2
📋 nBars_SL เดิม: 2
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        0.700    0.620      5.00         9.5        🏆 BEST    
3        0.300    0.380      1.00         15.5                 
4        0.300    0.380      1.00         15.5                 
5        0.700    0.620      5.00         9.5                  
6        0.000    0.200      -2.00        20.0                 
7        0.000    0.200      -2.00        20.0                 
8        0.000    0.200      -2.00        20.0                 
9        0.000    0.200      -2.00        20.0                 
10       0.000    0.200      -2.00        20.0                 
11       0.000    0.200      -2.00        20.0                 
12       0.000    0.200      -2.00        20.0                 
13       0.000    0.200      -2.00        20.0                 
14       0.500    0.500      3.00         12.5                 
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 2
   🔸 nBars_SL ใหม่: 2
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.7000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 2 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend: 2
📁 ไฟล์: M60_GOLD_counter_trend_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend: 2

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following_Sell: 2
📋 nBars_SL เดิม: 2
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following_Sell:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        0.700    0.620      5.00         9.5        🏆 BEST    
3        0.300    0.380      1.00         15.5                 
4        0.300    0.380      1.00         15.5                 
5        0.700    0.620      5.00         9.5                  
6        0.000    0.200      -2.00        20.0                 
7        0.000    0.200      -2.00        20.0                 
8        0.000    0.200      -2.00        20.0                 
9        0.000    0.200      -2.00        20.0                 
10       0.000    0.200      -2.00        20.0                 
11       0.000    0.200      -2.00        20.0                 
12       0.000    0.200      -2.00        20.0                 
13       0.000    0.200      -2.00        20.0                 
14       0.500    0.500      3.00         12.5                 
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 2
   🔸 nBars_SL ใหม่: 2
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.7000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 2 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following_Sell: 2
📁 ไฟล์: M60_GOLD_trend_following_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following_Sell: 2

======================================================================
📋 สรุปผลการทดสอบ Optimal nBars_SL สำหรับ GOLD MM60
======================================================================
🔸 trend_following:
   nBars_SL: 2 → 2 (+0 bars)
   Best Score: 1.0000
🔸 counter_trend:
   nBars_SL: 2 → 2 (+0 bars)
   Best Score: 0.7000
🔸 trend_following_Sell:
   nBars_SL: 2 → 2 (+0 bars)
   Best Score: 0.7000

💡 คำแนะนำการใช้งาน:
   🔄 Trend Following (2 bars): เน้น protection, เหมาะกับ volatile market
   ⚡ Counter Trend (2 bars): เน้น quick exit, เหมาะกับ scalping
   ⚡ Counter Trend (2 bars): เน้น quick exit, เหมาะกับ scalping

✅ การทดสอบ Optimal nBars_SL เสร็จสิ้น
💾 บันทึกผลลัพธ์แล้ว: 3 scenarios
✅ ผลการทดสอบ Optimal nBars SL:
   - trend_following: 2
   - counter_trend: 2
   - trend_following_Sell: 2
✅ ทดสอบ Optimal Parameters เสร็จสิ้น

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 332
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate (fallback): 22
🔍 Debug ไฟล์ CSV_Files_Fixed/GOLD_H1_FIXED.csv:
   metrics: {'accuracy': 0.9958474889265498, 'f1_score': 0.9964459759785701, 'auc': 0.7326471269908588, 'train_samples': 27957.0, 'test_samples': 9192.0}
   cv_results: {'best_score': 0.8242099591974615}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.0000    | 0.9958 |
| AUC         | 0.5000    | 0.7326 |
| F1 Score    | 0.0000    | 0.0000 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'accuracy': 0.9958474889265498, 'f1_score': 0.9964459759785701, 'auc': 0.7326471269908588, 'train_samples': 27957.0, 'test_samples': 9192.0}
🔍 Debug cv_results from result_dict: {'best_score': 0.8242099591974615}
🔍 Final model_metrics: {'avg_accuracy': 0.9958474889265498, 'avg_f1_score': 0.5, 'avg_auc': 0.7326471269908588, 'total_train_samples': 51, 'total_test_samples': 17}
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_GOLD_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_GOLD)
🔍 Debug loaded time_filters: {'days': [], 'hours': [], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 4.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Tuesday': {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 5.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}, 'Wednesday': {'win_rate': 0.0, 'expectancy': -1293.0000000000064, 'total_trades': 2.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.5}, 'Thursday': {'win_rate': 0.0, 'expectancy': -151.66666666667274, 'total_trades': 3.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.3333333333333333}, 'Friday': {'win_rate': 0.0, 'expectancy': -468.3333333333394, 'total_trades': 3.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.6666666666666666}}, 'hours': {4: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 9: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 237.00000000000045, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': -590.8000000000038, 'total_trades': 5.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': -755.5000000000064, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': -151.66666666667274, 'total_trades': 3.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}}}}
   ⚠️ time_filters ว่าง - สร้าง default
   ✅ สร้าง default time_filters: {'days': [0, 1, 2, 3, 4], 'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], 'source': 'default'}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: GOLD, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (85, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Sell': 84, 'Buy': 1}
🔍 calculate_trade_metrics: input shape = (1, 338)
   📊 Total trades: 1
   💰 Winning trades: 0/1
   💰 Total profit: 0.00
   ✅ Calculated metrics: {'count': 1, 'win_rate': 0.0, 'expectancy': 0.0, 'trade_accuracy': 0.0, 'trade_f1_score': 0, 'trade_auc': 0.5}
🔍 calculate_trade_metrics: input shape = (84, 338)
   📊 Total trades: 84
   💰 Winning trades: 10/84
   💰 Total profit: -1755.00
   ✅ Calculated metrics: {'count': 84, 'win_rate': 11.904761904761903, 'expectancy': -20.892857142858443, 'trade_accuracy': 0.11904761904761903, 'trade_f1_score': 0.14285714285714282, 'trade_auc': 0.5476190476190477}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: GOLD_M60
   Current F1: 0.5
   Current AUC: 0.7326471269908588
   Previous F1: 0.5
   Previous AUC: 0.6913602483338334
   Summary length: 3
⚠️ ⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน 0.0000, AUC เปลี่ยน 0.0413
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: GOLD, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.11764705882352941, 'expectancy': -20.647058823530696, 'profit_factor': 0.8211009174311839, 'count': 85, 'max_drawdown': 165.21739130435333}
   - Performance data prepared: {'symbol': 'GOLD', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.11764705882352941, 'expectancy': -20.647058823530696, 'profit_factor': 0.8211009174311839, 'num_trades': 85, 'max_drawdown': 165.21739130435333, 'model_accuracy': 0.9958474889265498, 'model_auc': 0.7326471269908588, 'model_f1': 0, 'training_date': '2025-09-14T16:02:54.845403', 'training_type': 'multi_model', 'num_features': 22}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_GOLD
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ GOLD MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_GOLD\performance_summary.json
   📁 ขนาดไฟล์: 767 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)
file CSV_Files_Fixed/USDJPY_H1_FIXED.csv main_round 1 symbol USDJPY timeframe M60

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
threshold (backward compatibility) : threshold 0.65 default 0.3

🏗️ เปิดใช้งาน load optimal nbars (backward compatibility)
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following: 2

📂 โหลดข้อมูลจาก LightGBM/Data_Trained/M60_USDJPY_features.csv

🔍 ตรวจสอบ columns ข้อมูล df ขั้นตอน create_features() : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

✅ ข้อมูลที่ส่งเข้า load_and_process_data : nBars_SL 2 confidence 0.65

🏗️ เปิดใช้งาน load and process data

🔍 กำลังสร้าง trade cycles...

🔄 Multi-Model: กำลังโหลด features จาก LightGBM/Multi/models/trend_following\M60_USDJPY_features.pkl
✅ โหลดรายชื่อ Features ที่ Model ใช้สำเร็จ: 24 features
1. Bar_CL_HL
2. MACD_line_x_PriceMove
3. IsMorning
4. Volume_Lag_20
5. Momentum5_x_Volatility10
6. IsNight
7. Bar_longwick
8. H12_Price_Move
9. IsEvening
10. IsAfternoon
... และอีก 14 features

First 5 rows of df:
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  13:00:00  100.083  100.248  100.083  100.151    1500  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  14:00:00  100.158  100.361  100.138  100.280    2766  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  15:00:00  100.280  100.368  100.157  100.299    2123  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  16:00:00  100.311  100.653  100.311  100.621    3329  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  17:00:00  100.622  100.648  100.491  100.608    1893  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
🔎 ใช้ entry_func (Multi-Model default): trend_following

🔄 โหลดโมเดล Multi-Model Architecture สำหรับ USDJPY MM60

🏗️ เปิดใช้งาน load scenario models

🔍 ตรวจสอบโมเดลที่มีอยู่สำหรับ USDJPY MM60
✅ trend_following: พร้อมใช้งาน
❌ trend_following_Buy: ไม่พบไฟล์โมเดล
❌ trend_following_Sell: ไม่พบไฟล์โมเดล
✅ counter_trend: พร้อมใช้งาน
✅ counter_trend_Buy: พร้อมใช้งาน
✅ counter_trend_Sell: พร้อมใช้งาน

📊 สรุป: 4/6 โมเดลพร้อมใช้งาน
⚠️ โมเดลที่ขาดหายไป: ['trend_following_Buy', 'trend_following_Sell']
🔍 กำลังโหลดโมเดลสำหรับ USDJPY MM60
📁 Base folder: LightGBM/Multi/models
🎯 Strategy: use_available
📋 จะโหลด 4 scenarios: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']

🔍 โหลด trend_following:
  📄 Model: LightGBM/Multi/models\trend_following\M60_USDJPY_trained.pkl
  📄 Features: LightGBM/Multi/models\trend_following\M60_USDJPY_features.pkl
  📄 Scaler: LightGBM/Multi/models\trend_following\M60_USDJPY_scaler.pkl
✅ โหลดโมเดล trend_following สำเร็จ
  📊 Features: 24 features

🔍 โหลด counter_trend:
  📄 Model: LightGBM/Multi/models\counter_trend\M60_USDJPY_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend\M60_USDJPY_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend\M60_USDJPY_scaler.pkl
✅ โหลดโมเดล counter_trend สำเร็จ
  📊 Features: 24 features

🔍 โหลด counter_trend_Buy:
  📄 Model: LightGBM/Multi/models\counter_trend_Buy\M60_USDJPY_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Buy\M60_USDJPY_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Buy\M60_USDJPY_scaler.pkl
✅ โหลดโมเดล counter_trend_Buy สำเร็จ
  📊 Features: 24 features

🔍 โหลด counter_trend_Sell:
  📄 Model: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_trained.pkl
  📄 Features: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_features.pkl
  📄 Scaler: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_scaler.pkl
✅ โหลดโมเดล counter_trend_Sell สำเร็จ
  📊 Features: 23 features

📊 สรุปการโหลดโมเดล: 4/6 โมเดล
⚠️ โหลดโมเดลได้ไม่ครบ - อาจส่งผลต่อประสิทธิภาพการทำนาย
📋 โมเดลที่โหลดได้: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📋 โมเดลที่ขาดหายไป: ['trend_following_Buy', 'trend_following_Sell']
✅ โหลดโมเดล Multi-Model สำเร็จ: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following_Buy (ใช้จาก trend_following): 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ trend_following_Sell (ใช้จาก trend_following): 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend_Buy: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Buy: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend_Sell: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Sell: 0.6500
🎯 Scenario Thresholds: {'trend_following': 0.65, 'counter_trend': 0.65, 'trend_following_Buy': 0.65, 'trend_following_Sell': 0.65, 'counter_trend_Buy': 0.65, 'counter_trend_Sell': 0.65}

🏗️ เปิดใช้งาน try trade with threshold adjustment

🔧 เริ่มทดสอบ Multi-Model ด้วย reduce_threshold เริ่มต้น 1.0 (threshold จริง: 0.6500)

============================================================
🧪 ครั้งที่ 1: ทดสอบ reduce_threshold = 1.0 (threshold จริง: 0.6500) (Multi-Model)
============================================================
ใช้ Multi-Model พร้อม scenario_thresholds

🏗️ เปิดใช้งาน create trade cycles with multi model

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 38308 (51.5%)
  downtrend: 30527 (41.1%)
  sideways: 5520 (7.4%)
📊 โหลด threshold สำหรับ trend_following: 0.6500
📊 โหลด threshold สำหรับ counter_trend: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Buy: 0.6500
📊 โหลด threshold สำหรับ counter_trend_Sell: 0.6500
🎯 Scenario Thresholds: {'trend_following': 0.65, 'counter_trend': 0.65, 'counter_trend_Buy': 0.65, 'counter_trend_Sell': 0.65}
📊 ใช้ Multi-Model: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
✅ ใช้ Multi-Model Architecture พร้อม 2 scenarios

🏗️ เปิดใช้งาน create trade cycles with model : reduce factor 1.0000

🔄 ใช้ Multi-Model Architecture: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📋 Scenario Model Mapping: {'trend_following': 'trend_following', 'counter_trend': 'counter_trend', 'trend_following_Buy': 'trend_following', 'trend_following_Sell': 'trend_following', 'counter_trend_Buy': 'counter_trend_Buy', 'counter_trend_Sell': 'counter_trend_Sell'}
🔍 ตรวจสอบโมเดลที่โหลดมาแล้ว: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']
📊 Scenario trend_following ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend ใช้: ✅ โมเดลตัวเอง
📊 Scenario trend_following_Buy ใช้: ✅ โมเดล trend_following
📊 Scenario trend_following_Sell ใช้: ✅ โมเดล trend_following
📊 Scenario counter_trend_Buy ใช้: ✅ โมเดลตัวเอง
📊 Scenario counter_trend_Sell ใช้: ✅ โมเดลตัวเอง
✅ Features สะอาด: 24 features (ไม่มี raw price หรือ data leakage)

🤖 สถานะการใช้งานโมเดลตาม Scenario:
   trend_following: ✅ ใช้โมเดลตัวเอง
   counter_trend: ✅ ใช้โมเดลตัวเอง
   trend_following_Buy: ✅ ใช้โมเดล trend_following
   trend_following_Sell: ✅ ใช้โมเดล trend_following
   counter_trend_Buy: ✅ ใช้โมเดลตัวเอง
   counter_trend_Sell: ✅ ใช้โมเดลตัวเอง
🔍 ใช้ start_index = 50 (ข้อมูลทั้งหมด 74355 แถว)

▶️ เริ่ม Backtest จาก index: {start_index}

🔍 สรุปการตรวจสอบ Look-Ahead Bias
- จำนวนการทำนายทั้งหมด: 74305
- จำนวนครั้งที่พบ NaN ใน Features: 0
- จำนวนครั้งที่พบ Features อาจมีปัญหา: 0
✅ ไม่พบ ปัญหาการใช้ข้อมูลจากอนาคต (Look-Ahead Bias)
✅ พบการเทรด 106 รายการด้วย reduce_threshold 1.0 (threshold จริง: 0.6500)
📊 ข้อมูลเพียงพอสำหรับ optimization: ดี (train=63, val=21, test=22)
🎉 สำเร็จ! ใช้ threshold สุดท้าย: 0.6500 (Multi-Model)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_06a_trade_df.csv

📌 ข้อมูลก่อนตรวจสอบ:
จำนวนแถวข้อมูลทั้งหมด: 74355 ตัวอย่างข้อมูล df
         Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0  2013.07.18  13:00:00  100.083  100.248  100.083  100.151    1500  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
1  2013.07.18  14:00:00  100.158  100.361  100.138  100.280    2766  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
2  2013.07.18  15:00:00  100.280  100.368  100.157  100.299    2123  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
3  2013.07.18  16:00:00  100.311  100.653  100.311  100.621    3329  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
4  2013.07.18  17:00:00  100.622  100.648  100.491  100.608    1893  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0

[5 rows x 332 columns]
จำนวนการซื้อขายที่พบ: 106 ตัวอย่างข้อมูล trade_df
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  ...  ATR at Entry  BB Width at Entry  RSI14 at Entry  Pct_Risk  Pct_Reward  Volume MA20 at Entry  Volume Spike at Entry
0 2013-07-31 17:00:00       98.335 2013-07-31 17:00:00      98.335       Sell     0.0          2  ...      0.233786           0.799855       62.240627  0.000000    0.002237               1935.00                      1
1 2013-09-23 09:00:00       99.026 2013-09-23 09:00:00      99.026       Sell     0.0          0  ...      0.098357           0.672110       37.294999  0.000000    0.004645               1163.15                      0
2 2013-09-23 10:00:00       98.947 2013-09-23 19:00:00      98.947       Sell     0.0          0  ...      0.102214           0.740450       33.788219  0.000000    0.005154               1185.20                      1
3 2013-10-01 05:00:00       98.488 2013-10-01 06:00:00      98.488       Sell     0.0          1  ...      0.203000           1.133778       63.224613  0.000000    0.007747               1706.30                      1
4 2014-10-17 18:00:00      106.647 2014-10-17 22:00:00     106.789       Sell  -142.0          4  ...      0.188357           0.532590       63.538326  0.001331    0.003994               1821.10                      1

[5 rows x 20 columns]

📊 สถิติการซื้อขาย:
========================================
ประเภท              ค่าสถิติ            
----------------------------------------
🏗️ เปิดใช้งาน analyze trade performance
📈 สถิติสำหรับ Buy Trades:
Win%                0.00                
Expectancy          0.00                
📈 สถิติสำหรับ Sell Trades:
Win%                14.29               
Expectancy          -78.24              
📈 สถิติสำหรับ Buy_sell Trades:
Win%                14.29               
Expectancy          -78.24              
========================================

📊 สถิติรายวัน:
วัน       Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
Monday    30.00          10                  
Tuesday   0.00           30                  
Wednesday 0.00           25                  
Thursday  6.25           16                  
Friday    8.00           25                  
========================================

📊 สถิติรายชั่วโมง:
ชั่วโมง   Win Rate (%)   จำนวนการซื้อขาย     
---------------------------------------------
4         0.00           7                   
5         0.00           3                   
6         50.00          2                   
7         0.00           1                   
9         0.00           3                   
10        0.00           3                   
11        10.00          10                  
12        0.00           3                   
13        0.00           2                   
14        0.00           6                   
15        0.00           5                   
16        9.09           11                  
17        7.69           13                  
18        4.55           22                  
19        0.00           11                  
20        0.00           1                   
21        33.33          3                   
========================================

🔍 กำลังรวม features กับ trade data...

ตัวอย่าง Entry_DateTime ใน trade_df: 0   2013-07-31 17:00:00
1   2013-09-23 09:00:00
2   2013-09-23 10:00:00
3   2013-10-01 05:00:00
4   2014-10-17 18:00:00
Name: Entry_DateTime, dtype: datetime64[ns]
ตัวอย่าง Merge_Key_Time ที่จะใช้ join: 0   2013-07-31 16:55:00
1   2013-09-23 08:55:00
2   2013-09-23 09:55:00
3   2013-10-01 04:55:00
4   2014-10-17 17:55:00
Name: Merge_Key_Time, dtype: datetime64[ns]
ตัวอย่าง DateTime ใน df: 0   2013-07-18 13:00:00
1   2013-07-18 14:00:00
2   2013-07-18 15:00:00
3   2013-07-18 16:00:00
4   2013-07-18 17:00:00
Name: DateTime, dtype: datetime64[ns]
🔍 กำลังทำการ merge_asof โดยใช้คอลัมน์ 313 features : ['DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🔧 คำนวณฟีเจอร์เวลา (Hour, DayOfWeek) จาก Timestamp ของ Feature ที่ถูกต้อง (แท่งก่อนหน้า)

📌 ตรวจสอบ Missing Values หลัง Merge:
✅ ไม่พบ Missing Values ใน DataFrame

📌 ข้อมูลหลังรวม features:
จำนวนแถว: 106
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour
0 2013-07-31 17:00:00       98.335 2013-07-31 17:00:00      98.335       Sell     0.0          2          17
1 2013-09-23 09:00:00       99.026 2013-09-23 09:00:00      99.026       Sell     0.0          0           9
2 2013-09-23 10:00:00       98.947 2013-09-23 19:00:00      98.947       Sell     0.0          0          10
3 2013-10-01 05:00:00       98.488 2013-10-01 06:00:00      98.488       Sell     0.0          1           5
4 2014-10-17 18:00:00      106.647 2014-10-17 22:00:00     106.789       Sell  -142.0          4          18

🔍 กำลังสร้าง target variable...

🔍 กำลังสร้าง target variable...

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_06b_trade_df_merge.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง merge : จำนวน 332
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']

🏗️ เปิดใช้งาน process trade targets
📊 Profit column status: 106/106 valid values

🏗️ เปิดใช้งาน create multiclass target (Logic ใหม่)
📊 Multi-class Target Statistics:
  Class 0 (strong_sell): 6 samples (5.7%)
  Class 1 (weak_sell): 63 samples (59.4%)
  Class 2 (no_trade): 36 samples (34.0%)
  Class 3 (weak_buy): 1 samples (0.9%)
✅ Multi-class Target ถูกต้อง: 2 classes พร้อมใช้งาน

📊 Multi-class Target Distribution:
Target_Multiclass
0     6
1    63
2    36
3     1
Name: count, dtype: int64
Class 0 (strong_sell): 6 trades, Profit range: 343.0 to 1431.0
Class 1 (weak_sell): 63 trades, Profit range: 0.0 to 0.0
Class 2 (no_trade): 36 trades, Profit range: -389.0 to -77.0
Class 3 (weak_buy): 1 trades, Profit range: 0.0 to 0.0

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_07_trade_df_valid_trades.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📊 การกระจายของ Target ต่างๆ:
1. Target หลัก (Binary):
Main_Target
0    100
1      6
Name: count, dtype: int64

2. Target สำหรับ Buy Trades:
Target_Buy
0    1
Name: count, dtype: int64

3. Target สำหรับ Sell Trades:
Target_Sell
0    99
1     6
Name: count, dtype: int64

✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_08a_trade_df_target.csv
🔍 ตรวจสอบ columns ข้อมูล df หลัง trade_targets : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

📌 ข้อมูลหลังสร้าง target variable:
จำนวนแถว: 106
ตัวอย่างคอลัมน์ใหม่ 5 แถว:
           Entry Time       EMA50      EMA100      EMA200      RSI14
0 2013-07-31 17:00:00   98.078151   98.368276   98.848412  62.240627
1 2013-09-23 09:00:00   99.198760   99.170699   99.212638  37.294999
2 2013-09-23 10:00:00   99.188730   99.166190   99.209955  33.788219
3 2013-10-01 05:00:00   98.260608   98.427246   98.654673  63.224613
4 2014-10-17 18:00:00  106.377643  106.649469  107.176851  63.538326

📌 ข้อมูลหลังสร้าง target:
การกระจายของ Target:
Target
0    100
1      6
Name: count, dtype: int64

🔍 เริ่มกระบวนการเลือก Features...

🏗️ เปิดใช้งาน select features
⚠️ Feature 'Volume_MA_20' not found in DataFrame. Skipping.
ℹ️ Potential features for model input (Pre-Trade Only): ['DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_15', 'ADX_zone_25', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']+['Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20'] = 416 features considered.

🔍 ความสัมพันธ์กับ Target (ทั้งหมด):
Target                     1.000000
Volume_Change_2            0.270427
ADX_zone_15                0.263637
BB_Break_CL                0.249737
H12_Price_Move             0.233560
                             ...   
RSI_Divergence_i6               NaN
RSI_Divergence_i2               NaN
RSI_counter                     NaN
Price_EMA50_x_RSI_trend         NaN
H4_Bar_DTB                      NaN
Name: Target, Length: 301, dtype: float64

📊 ค่า VIF ของ Features:
              feature           VIF
0     Volume_Change_2  1.401737e+07
1         ADX_zone_15  5.211029e+06
2         BB_Break_CL  6.613528e+07
3      H12_Price_Move  8.910455e+06
4       H8_Price_Move  8.588416e+07
..                ...           ...
268  ATR_x_PriceRange  2.037782e+07
269      Volume_Spike  8.841935e+06
270        Price_Move  8.236873e+08
271      D1_MACD_line  1.439904e+06
272      D1_Bar_CL_OC  6.381759e+06

[273 rows x 2 columns]

🚫 Features ถูกตัดออกเนื่องจาก VIF สูง: ['Volume_Change_2', 'ADX_zone_15', 'BB_Break_CL', 'H12_Price_Move', 'H8_Price_Move', 'MA_Cross_50_200', 'H12_Bar_OSB', 'Bar_DTB', 'Bar_FVG', 'MA_Cross_50_100', 'Momentum5_x_Volatility10', 'Price_Strangth', 'H8_Price_Range', 'H4_MACD_line', 'ADX_14_x_RollingVol15', 'H4_Bar_SW', 'EMA_Cross_EMA10', 'RSI14_Lag_5', 'D1_Bar_longwick', 'MACD_signal_x_ADX', 'EMA_diff_x_BBwidth', 'ATR_ROC_i8', 'DMP_14_Lag_5', 'RSI_ROC_i8', 'H2_MACD_deep', 'H8_Bar_SW', 'ADX_Deep', 'MACD_signal_x_RSI14', 'H8_Price_Strangth', 'STO_Oversold', 'EMA_Cross_EMA15', 'Price_EMA50', 'Slope_EMA50', 'RSI_ROC_i2', 'RSI14', 'RSI_Zone', 'MACD_12_26_9_Lag_1', 'Volume_Lag_20', 'H12_Volume_Momentum', 'MACD_12_26_9_Lag_3', 'RSI14_x_StochD', 'H8_Bar_OSB', 'MACD_12_26_9_Lag_2', 'EMA_diff_x_ATR', 'Close_Std_5', 'Rolling_Close_15', 'H12_Volume_TrendStrength', 'ADX_cross', 'RSI14_x_StochK', 'H12_Price_Range', 'RSI14_Lag_1', 'STOCHk_14_3_3_Lag_3', 'DMN_14_Lag_5', 'DMP_14_Lag_3', 'Close_Return_5', 'STOCHd_14_3_3_Lag_2', 'MACD_line_x_PriceMove', 'ATR_ROC_i2', 'H2_Volume_Momentum', 'STOCHd_14_3_3_Lag_1', 'H8_Bar_CL_OC', 'Dist_EMA50', 'STOCHd_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_1', 'H12_Price_Strangth', 'Bar_OSB', 'Close_Return_2', 'PullBack_100_Down', 'PullBack_100_Up', 'H4_Bar_CL_OC', 'H4_Volume_Momentum', 'Slope_EMA100', 'H2_Bar_TL', 'MA_Cross_100_200', 'Close_Std_10', 'D1_Volume_TrendStrength', 'H2_Bar_CL_HL', 'MACD_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_2', 'DMN_14_Lag_1', 'RSI_signal_EMA12', 'H2_Volume_TrendStrength', 'D1_Price_Range', 'ADX_14_x_ATR', 'ADX_14_x_BBwidth', 'H4_Price_Move', 'RSI14_x_PullBack_50_Down', 'H12_Bar_SW', 'D1_Volume_Momentum', 'H8_MACD_line', 'H4_Bar_TL', 'H8_Volume_TrendStrength', 'H4_Price_Strangth', 'MACDs_12_26_9_Lag_1', 'H8_Bar_FVG', 'DMP_14_Lag_1', 'D1_Price_Move', 'EMA_diff_50_100', 'RSI_ROC_i4', 'H4_Bar_CL_HL', 'EMA_diff_x_RSI14', 'DMP_14_Lag_2', 'H2_Bar_OSB', 'ADX_14_Lag_2', 'Volume_Lag_10', 'DMN_14_Lag_3', 'Dist_EMA100', 'H4_Volume_TrendStrength', 'H2_Volume_Spike', 'EMA_Cross_EMA5', 'H8_MACD_deep', 'Price_Range', 'RSI14_Lag_3', 'STOCHk_14_3_3_Lag_5', 'H12_Bar_CL_HL', 'H4_Price_Range', 'H2_Price_Move', 'ADX_14_Lag_1', 'ADX_14_Lag_3', 'BB_Break_HL', 'H8_Bar_TL', 'RSI14_Lag_2', 'EMA_diff_50_200', 'ADX_14_Lag_5', 'STO_zone', 'Low_Lag_20', 'Open_Lag_20', 'Close_Lag_20', 'MACDs_12_26_9_Lag_2', 'High_Lag_20', 'Low_Lag_15', 'Low_Lag_10', 'H4_Bar_longwick', 'IsNight', 'H12_Volume_Spike', 'Open_Lag_15', 'Close_Lag_15', 'Close_Lag_10', 'Open_Lag_10', 'High_Lag_15', 'Low_Lag_30', 'High_Lag_10', 'DayOfWeek', 'Close_Lag_30', 'H12_MACD_deep', 'H4_MACD_signal', 'Close_MA_20', 'Open_Lag_30', 'EMA50_Lag_5', 'High_Lag_30', 'ATR_ROC_i6', 'EMA50_Lag_3', 'EMA50_Lag_2', 'EMA50_Lag_1', 'Close_Lag_50', 'EMA100_Lag_5', 'EMA100_Lag_3', 'EMA100_Lag_2', 'EMA100_Lag_1', 'Bar_CL_HL', 'Close_MA_10', 'High_Lag_50', 'Low_Lag_50', 'Close_Lag_2', 'Open_Lag_1', 'RSI_x_VolumeSpike', 'Low_Lag_1', 'Low_Lag_2', 'High_Lag_2', 'EMA200_Lag_5', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_1', 'Close_MA_3', 'Open_Lag_50', 'High_Lag_1', 'Close_Lag_3', 'Open_Lag_2', 'IsEvening', 'Close_Lag_1', 'Low_Lag_3', 'Close_MA_5', 'Volume_Lag_50', 'High_Lag_3', 'High_Lag_5', 'Open_Lag_3', 'Open_Lag_5', 'Low_Lag_5', 'RSI_signal_EMA8', 'D1_Bar_OSB', 'Volume_Lag_1', 'Close_Lag_5', 'Volume_Lag_30', 'H2_Bar_CL_OC', 'H2_MACD_signal', 'EMA_diff_100_200', 'BB_Outside', 'H8_Volume_Momentum', 'RSI14_x_PriceMove', 'H2_Bar_FVG', 'H4_Bar_FVG', 'ATR_Lag_1', 'Volume_Lag_15', 'H2_Price_Strangth', 'DMN_14_Lag_2', 'BB_width_Lag_3', 'H12_Bar_TL', 'ATR_ROC_i4', 'STOCHd_14_3_3_Lag_5', 'BB_width', 'H4_MACD_deep', 'MACDs_12_26_9_Lag_3', 'BB_width_Lag_2', 'Volume_Lag_2', 'ATR_Lag_2', 'BB_width_Lag_5', 'EMA50_x_RollingVol5', 'RSI_signal_EMA4', 'BB_width_Lag_1', 'Close_Std_20', 'IsAfternoon', 'ATR_Deep', 'Volume_Change_3', 'H8_MACD_signal', 'Bar_CL_OC', 'H8_Bar_longwick', 'H2_MACD_line', 'PullBack_50_Down', 'PullBack_50_Up', 'Volume_MA20', 'D1_Bar_CL_HL', 'ATR_Lag_3', 'RSI_ROC_i6', 'H2_Bar_longwick', 'STO_overbought', 'Volume_MA_3', 'Volume_Change_1', 'Bar_TL', 'Close_Return_3', 'D1_Bar_SW', 'D1_Bar_TL', 'MACDs_12_26_9_Lag_5', 'H2_Bar_DTB', 'Slope_EMA200', 'Bar_CL', 'Volume_Change_5', 'Volume_MA_10', 'RSI14_x_Volume', 'RSI14_x_ATR', 'Close_Return_1', 'D1_Price_Strangth', 'H4_Bar_OSB', 'D1_Bar_FVG', 'Dist_EMA200', 'ADX_zone_25', 'Hour', 'Volume_Lag_5', 'RSI14_x_BBwidth', 'H4_Volume_Spike', 'H8_Volume_Spike', 'Bar_SW', 'Volume_Lag_3', 'H2_Price_Range', 'H12_Bar_CL_OC', 'D1_MACD_signal', 'D1_Volume_Spike', 'H12_Bar_FVG', 'RSI14_x_PullBack_50_Up', 'ATR_x_PriceRange', 'Volume_Spike', 'Price_Move', 'D1_MACD_line', 'D1_Bar_CL_OC']

เริ่มขั้นตอนการตรวจสอบ features ที่ต้องใช้จากการทำ analyze cross asset feature importance

👍 โหลดรายชื่อ Features ที่จำเป็นจากไฟล์: LightGBM/Multi\feature_importance\M60_must_have_features.pkl (22 Features)

featuresasset_feature_importance : len  22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
👍 เพิ่ม Feature ที่จำเป็น 'Bar_longwick' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_20' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'STO_zone' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Bar_CL_HL' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsMorning' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'MACD_line_x_PriceMove' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsNight' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'H12_Price_Range' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'H12_Price_Move' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsAfternoon' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_5' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Change_1' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_10' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Volume_Lag_30' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Price_Range' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Hour' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'ATR_ROC_i2' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Bar_SW' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Momentum5_x_Volatility10' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'IsEvening' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'Price_Strangth' เข้าไปในรายการ
👍 เพิ่ม Feature ที่จำเป็น 'DayOfWeek' เข้าไปในรายการ

🔍 จำนวน features ที่เลือกได้: 22

✅ Final selected features for training: 22 features
📋 Top 10 features:
1. Bar_longwick (corr: 0.0005)
2. Volume_Lag_20 (corr: 0.1312)
3. STO_zone (corr: 0.0626)
4. Bar_CL_HL (corr: 0.0562)
5. IsMorning (corr: 0.0080)
6. MACD_line_x_PriceMove (corr: 0.1146)
7. IsNight (corr: 0.0606)
8. H12_Price_Range (corr: 0.1231)
9. H12_Price_Move (corr: 0.2336)
10. IsAfternoon (corr: 0.0350)
... และอีก 12 features
💾 บันทึก features (pkl): LightGBM/Multi\feature_selected\USDJPY_M60_selected_features.pkl
📝 บันทึก features (txt): LightGBM/Multi\feature_selected\USDJPY_M60_selected_features.txt

📌 สรุป Features ที่จะใช้สำหรับโมเดล:
จำนวน Features: 22
1. Bar_longwick
2. Volume_Lag_20
3. STO_zone
4. Bar_CL_HL
5. IsMorning
6. MACD_line_x_PriceMove
7. IsNight
8. H12_Price_Range
9. H12_Price_Move
10. IsAfternoon
11. Volume_Lag_5
12. Volume_Change_1
13. Volume_Lag_10
14. Volume_Lag_30
15. Price_Range
... และอีก 7 features

🔍 กำลังตรวจสอบข้อมูลก่อนฝึกโมเดล...

📊 การกระจายของ Target:
Target
0    0.943396
1    0.056604
Name: proportion, dtype: float64
อัตราส่วน Class Imbalance: 0.06
⚠️ พบ Class Imbalance รุนแรง (อัตราส่วนน้อยกว่า 20%)
✅ ไม่พบ missing values ใน features

📊 สถิติพื้นฐานของ features:
                          count         mean          std       min          25%          50%          75%           max
Bar_longwick              106.0     0.127412     1.939897 -6.578947    -0.865116     0.213224     1.015209      9.307692
Volume_Lag_20             106.0  3140.179245  2754.708550  0.000000  1310.000000  2096.500000  4436.750000  13298.000000
STO_zone                  106.0     0.547170     0.840998 -1.000000     1.000000     1.000000     1.000000      1.000000
Bar_CL_HL                 106.0    -0.235849     0.426545 -1.000000     0.000000     0.000000     0.000000      0.000000
IsMorning                 106.0     0.179245     0.385380  0.000000     0.000000     0.000000     0.000000      1.000000
MACD_line_x_PriceMove     106.0    -0.051274     0.120894 -0.645000    -0.103000    -0.048500     0.002000      0.349000
IsNight                   106.0     0.094340     0.293689  0.000000     0.000000     0.000000     0.000000      1.000000
H12_Price_Range           106.0     0.803481     0.661693  0.168000     0.427750     0.608000     0.976000      4.970000
H12_Price_Move            106.0     0.076415     0.651475 -3.401000    -0.072000     0.127500     0.363000      1.540000
IsAfternoon               106.0     0.226415     0.420499  0.000000     0.000000     0.000000     0.000000      1.000000
Volume_Lag_5              106.0  3578.990566  2759.216664  0.000000  1657.250000  2463.000000  5057.250000  13901.000000
Volume_Change_1           106.0     0.212849     0.565204 -0.362866    -0.081878     0.016181     0.411304      3.793823
Volume_Lag_10             106.0  2767.622642  2499.159914  0.000000  1052.500000  1933.500000  3586.250000  14174.000000
Volume_Lag_30             106.0  4705.150943  9176.980382  0.000000  1567.750000  2641.500000  6070.500000  92900.000000
Price_Range               106.0     0.262396     0.194791  0.075000     0.148500     0.211500     0.312500      1.554000
Hour                      106.0    13.481132     4.678688  3.000000    10.000000    15.000000    17.000000     20.000000
ATR_ROC_i2                106.0     0.063305     0.071215 -0.134350     0.026086     0.065337     0.099789      0.327849
Bar_SW                    106.0    -0.301887     0.461257 -1.000000    -1.000000     0.000000     0.000000      0.000000
Momentum5_x_Volatility10  106.0     0.085761     0.370074 -0.431420    -0.004998     0.017493     0.060108      3.006507
IsEvening                 106.0     0.443396     0.499146  0.000000     0.000000     0.000000     1.000000      1.000000
Price_Strangth            106.0     0.452830     0.518828 -1.000000     0.000000     0.000000     1.000000      1.000000
DayOfWeek                 106.0     2.150943     1.322281  0.000000     1.000000     2.000000     3.000000      4.000000

🔍 กำลังตรวจสอบความสัมพันธ์ระหว่าง features...
✅ ไม่พบ features ที่มีความสัมพันธ์สูงเกินไป

🔍 กำลังแบ่งข้อมูลเป็น train/val/test...
การกระจายของ Target ในชุดข้อมูล:

📊 ใช้ Target Column: Target_Multiclass
Train: Target_Multiclass
1    0.523810
2    0.428571
0    0.031746
3    0.015873
Name: proportion, dtype: float64
Val: Target_Multiclass
1    0.761905
2    0.190476
0    0.047619
Name: proportion, dtype: float64
Test: Target_Multiclass
1    0.636364
2    0.227273
0    0.136364
Name: proportion, dtype: float64

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 1 : จำนวน 338

🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนแยกข้อมูล ครั้งที่ 2 : จำนวน 338
✅ บันทึกไฟล์ train, val, test เรียบร้อย

🏗️ เปิดใช้งาน analyze time filters

📊 Time Filter Analysis for USDJPY:
📅 Recommended Days: []
⏰ Recommended Hours: []
✅ บันทึก time filter ที่: LightGBM/Multi/thresholds/M60_USDJPY_time_filters.pkl

🔍 กำลังทำ Feature Scaling...
✅ ทำ Feature Scaling เรียบร้อยแล้ว
train_data : (    Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove   IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
0       1.897051      -0.788981  0.509902   0.534522  -0.459933              -0.006445 -0.324443  ...  0.621336    0.133859 -1.521772                  0.840439   1.082781       -0.869652   0.000000
1       0.649474      -0.381290 -1.961161  -1.870829   2.174229               1.455210 -0.324443  ... -1.018211   -2.761200 -1.521772                 -0.358596  -0.923548       -0.869652  -1.571810
2       0.223960      -0.443674 -1.961161   0.534522   2.174229               1.559614 -0.324443  ... -0.813267   -0.832827 -1.521772                 -0.399249  -0.923548       -0.869652  -1.571810
3      -0.873439       0.090542  0.509902   0.534522  -0.459933               0.329968 -0.324443  ... -1.837984   -0.406443  0.657129                 -0.121850  -0.923548       -0.869652  -0.785905
4      -1.213653       0.362922  0.509902   0.534522  -0.459933               0.457573 -0.324443  ...  0.826280   -0.478038  0.657129                 -0.155842   1.082781        1.019592   1.571810
..           ...            ...       ...        ...        ...                    ...       ...  ...       ...         ...       ...                       ...        ...             ...        ...
58      4.220465      -0.590407  0.509902   0.534522   2.174229               0.561977 -0.324443  ... -0.813267    0.132533 -1.521772                 -0.195055  -0.923548       -0.869652  -0.785905
59     -0.476403       0.367315  0.509902  -1.870829  -0.459933              -0.099248 -0.324443  ... -0.198437    0.764570  0.657129                 -0.261703  -0.923548       -0.869652  -0.785905
60      0.448970       0.196858  0.509902   0.534522  -0.459933               0.248765 -0.324443  ...  0.211449    0.119911  0.657129                 -0.155603  -0.923548        1.019592   1.571810
61      0.121227      -0.362838  0.509902   0.534522  -0.459933              -0.505263 -0.324443  ...  0.621336    1.210123  0.657129                 -0.193824   1.082781        1.019592   1.571810
62      0.461846      -0.272338  0.509902   0.534522  -0.459933               0.503974 -0.324443  ...  1.031223    0.886438  0.657129                  0.223232   1.082781        1.019592  -0.785905

[63 rows x 22 columns], 0     1
1     1
2     1
3     1
4     2
     ..
58    1
59    2
60    1
61    0
62    1
Name: Target_Multiclass, Length: 63, dtype: int32)
val_data : (    Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove   IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
63      0.119795      -0.172172 -1.961161  -1.870829   2.174229              -0.215253 -0.324443  ... -0.608324    0.894922 -1.521772                 -0.342735  -0.923548        1.019592  -1.571810
64     -0.852855       0.475388  0.509902   0.534522  -0.459933               0.167562 -0.324443  ...  0.621336    1.544263  0.657129                 -0.263855   1.082781        1.019592   1.571810
65     -0.384206       0.578190  0.509902   0.534522  -0.459933              -0.122449 -0.324443  ... -1.837984   -0.459666  0.657129                 -0.112752  -0.923548        1.019592   0.000000
66     -0.589057       4.815013  0.509902   0.534522  -0.459933               0.387970 -0.324443  ...  0.621336   -2.068987  0.657129                  0.260834   1.082781        1.019592   0.000000
67      0.786111       3.142074  0.509902   0.534522   2.174229               0.283566 -0.324443  ... -0.608324   -0.777796 -1.521772                  0.109602  -0.923548       -0.869652   0.785905
68      0.482793       2.905719  0.509902   0.534522  -0.459933              -0.992482 -0.324443  ...  0.416393    1.143766 -1.521772                  0.291571  -0.923548       -0.869652   1.571810
69     -0.479268      -1.283658 -1.961161   0.534522  -0.459933               1.524813  3.082207  ...  1.441110    0.279796  0.657129                 -2.952770  -0.923548        1.019592  -0.785905
70      0.125756       2.895175  0.509902   0.534522  -0.459933              -2.198927 -0.324443  ...  0.621336    0.569392  0.657129                 -0.167951   1.082781       -0.869652   1.571810
71      1.892440       1.296921 -1.961161  -1.870829  -0.459933               1.397208  3.082207  ...  1.441110    2.795779  0.657129                 -0.349899  -0.923548       -0.869652   0.000000
72     -0.251894       6.835016 -1.961161  -1.870829  -0.459933               2.383245 -0.324443  ...  0.006506    0.011069  0.657129                 -0.586182  -0.923548       -0.869652   1.571810
73     -0.578650       0.757433 -1.961161   0.534522  -0.459933               1.350806 -0.324443  ...  1.031223    0.460518  0.657129                 -1.441535   1.082781        1.019592   1.571810
74     -0.526422       5.245549  0.509902   0.534522  -0.459933               0.782385 -0.324443  ...  0.621336   -0.683487  0.657129                  1.026916   1.082781        1.019592   0.000000
75      0.241715       5.795580  0.509902   0.534522  -0.459933              -0.168851 -0.324443  ... -0.198437    2.040430  0.657129                  0.937359  -0.923548        1.019592   1.571810
76     -0.213709       3.422362  0.509902   0.534522   2.174229              -3.579379 -0.324443  ... -0.813267    0.381853  0.657129                 -0.338711  -0.923548       -0.869652  -0.785905
77     -1.002776       3.737795  0.509902  -1.870829  -0.459933              -0.609667 -0.324443  ...  0.416393    0.303121  0.657129                 -0.414291  -0.923548       -0.869652   0.785905
78      0.034801       4.214020  0.509902   0.534522  -0.459933              -1.572503 -0.324443  ...  0.826280   -1.290907  0.657129                 -0.074373   1.082781        1.019592   1.571810
79      0.366411       7.369232  0.509902   0.534522  -0.459933               0.051557 -0.324443  ...  0.211449    0.544119  0.657129                  0.715132  -0.923548       -0.869652  -0.785905
80     -0.528594       2.438280  0.509902   0.534522  -0.459933              -0.609667 -0.324443  ...  0.211449   -0.098272  0.657129                 -0.058469  -0.923548       -0.869652   0.000000
81     -0.001629       3.002370  0.509902   0.534522  -0.459933              -1.966918 -0.324443  ...  0.621336   -0.609926  0.657129                  0.541453   1.082781        1.019592  -1.571810
82     -0.633241      -0.111546  0.509902   0.534522  -0.459933               0.109560 -0.324443  ...  0.826280   -1.732975  0.657129                  0.180974   1.082781        1.019592   0.785905
83      0.174308       5.611065  0.509902   0.534522  -0.459933              -0.922879 -0.324443  ...  0.621336    3.907595  0.657129                  1.196202   1.082781        1.019592   1.571810

[21 rows x 22 columns], 63    0
64    1
65    1
66    1
67    1
68    1
69    1
70    1
71    1
72    2
73    1
74    1
75    1
76    1
77    2
78    2
79    1
80    1
81    2
82    1
83    1
Name: Target_Multiclass, dtype: int32)
test_data : (     Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove   IsNight  ...      Hour  ATR_ROC_i2    Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
84       0.705829       1.035085  0.509902   0.534522  -0.459933               0.213964 -0.324443  ...  0.826280    0.213070  0.657129                  1.859499   1.082781        1.019592  -1.571810
85       0.635938       1.815321  0.509902  -1.870829  -0.459933              -1.143287 -0.324443  ...  0.416393    0.746154 -1.521772                 -0.091328  -0.923548       -0.869652   0.000000
86      -1.171477       0.931405  0.509902   0.534522  -0.459933              -0.632868 -0.324443  ...  0.826280   -0.167077  0.657129                 -0.514651   1.082781       -0.869652   0.000000
87       0.054376       1.699340  0.509902  -1.870829   2.174229               4.645330 -0.324443  ... -0.608324    0.452872 -1.521772                 -0.647374  -0.923548       -0.869652   0.000000
88       0.110931       2.809068 -1.961161  -1.870829  -0.459933               2.139636 -0.324443  ...  1.031223    0.326375 -1.521772                 -0.436271   1.082781       -0.869652   0.000000
89      -0.635228       0.215310  0.509902   0.534522  -0.459933              -0.342857 -0.324443  ...  0.826280   -1.031505  0.657129                  0.003754   1.082781       -0.869652   0.785905
90       0.398127       3.131530  0.509902   0.534522   2.174229              -0.818475 -0.324443  ... -1.018211    0.649321 -1.521772                  0.528879  -0.923548       -0.869652  -0.785905
91       0.261086       3.811601  0.509902   0.534522  -0.459933               0.225564 -0.324443  ...  0.211449   -0.253410 -1.521772                  0.050436  -0.923548        1.019592   0.000000
92       0.099498       1.174789  0.509902  -1.870829  -0.459933               3.612891 -0.324443  ... -1.633041   -0.036653 -1.521772                 11.332663  -0.923548       -0.869652   0.785905
93       0.659228       2.758985  0.509902   0.534522  -0.459933               0.028357 -0.324443  ...  0.416393    0.155743 -1.521772                 -0.369726  -0.923548       -0.869652   0.000000
94       0.508552       2.100881  0.509902   0.534522  -0.459933              -0.598067 -0.324443  ...  1.031223    0.983993  0.657129                 -0.135235   1.082781        1.019592   0.000000
95       0.327767       3.112200  0.509902  -1.870829  -0.459933               2.499249 -0.324443  ...  0.416393   -0.196314  0.657129                 -0.607308  -0.923548       -0.869652   1.571810
96      -0.198584       9.646678  0.509902  -1.870829   2.174229              -6.885502 -0.324443  ... -1.018211   -0.331467 -1.521772                 18.265644  -0.923548       -0.869652   0.000000
97       0.374962      10.207254  0.509902   0.534522  -0.459933              -1.839313  3.082207  ... -2.042927    0.168934 -1.521772                  0.085055  -0.923548       -0.869652   1.571810
98       0.442971       8.162648 -1.961161   0.534522   2.174229               0.898389 -0.324443  ... -0.403381    0.166309  0.657129                 -2.377436  -0.923548        1.019592  -1.571810
99      -0.709852       4.091010 -1.961161   0.534522  -0.459933               0.933191 -0.324443  ...  0.826280   -0.140624  0.657129                 -0.118586   1.082781       -0.869652  -1.571810
100     -2.299704       7.121455  0.509902   0.534522  -0.459933               0.121160 -0.324443  ...  0.826280   -0.350143  0.657129                  1.197305   1.082781        1.019592   0.785905
101      0.668473       2.100002 -1.961161   0.534522   2.174229               0.933191 -0.324443  ... -0.403381    0.533212  0.657129                 -0.124533  -0.923548       -0.869652   1.571810
102     -0.939500       2.832791  0.509902   0.534522  -0.459933              -0.215253 -0.324443  ...  0.416393   -0.369810  0.657129                  0.232246  -0.923548       -0.869652  -0.785905
103      0.237960       2.559533  0.509902   0.534522  -0.459933              -0.853276 -0.324443  ...  1.031223    0.545340  0.657129                 -0.252659   1.082781        1.019592  -0.785905
104      0.947550       2.370624 -1.961161  -1.870829  -0.459933               0.991193 -0.324443  ...  1.236166   -0.351561 -1.521772                 -0.333160   1.082781       -0.869652   1.571810
105     -0.789583      -0.750320 -1.961161   0.534522  -0.459933               0.817186  3.082207  ...  1.441110    0.740332  0.657129                 -0.348537  -0.923548        1.019592   1.571810

[22 rows x 22 columns], 84     0
85     1
86     1
87     2
88     1
89     2
90     1
91     1
92     0
93     1
94     1
95     1
96     1
97     1
98     1
99     2
100    1
101    1
102    2
103    2
104    1
105    0
Name: Target_Multiclass, dtype: int32)
df :              Date      Time     Open     High      Low    Close  Volume  ... D1_Price_Strangth  D1_Volume_Spike  D1_Volume_Momentum  D1_Volume_TrendStrength  D1_MACD_line  D1_MACD_deep  D1_MACD_signal
0      2013.07.18  13:00:00  100.083  100.248  100.083  100.151    1500  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
1      2013.07.18  14:00:00  100.158  100.361  100.138  100.280    2766  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
2      2013.07.18  15:00:00  100.280  100.368  100.157  100.299    2123  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
3      2013.07.18  16:00:00  100.311  100.653  100.311  100.621    3329  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
4      2013.07.18  17:00:00  100.622  100.648  100.491  100.608    1893  ...              -1.0              0.0                 1.0                      0.0           0.0           0.0             0.0
...           ...       ...      ...      ...      ...      ...     ...  ...               ...              ...                 ...                      ...           ...           ...             ...
74350  2025.07.11  19:00:00  147.284  147.400  147.248  147.362    2743  ...               0.0              0.0                -1.0                     -1.0           1.0           1.0             1.0
74351  2025.07.11  20:00:00  147.362  147.412  147.314  147.385    1683  ...               0.0              0.0                -1.0                     -1.0           1.0           1.0             1.0
74352  2025.07.11  21:00:00  147.379  147.444  147.342  147.441    2119  ...               0.0              0.0                -1.0                     -1.0           1.0           1.0             1.0
74353  2025.07.11  22:00:00  147.436  147.476  147.366  147.369    2334  ...               0.0              0.0                -1.0                     -1.0           1.0           1.0             1.0
74354  2025.07.11  23:00:00  147.368  147.465  147.339  147.430    1904  ...               0.0              0.0                -1.0                     -1.0           1.0           1.0             1.0

[74355 rows x 332 columns]
trade_df :              Entry Time  Entry Price           Exit Time  Exit Price Trade Type  Profit  Entry Day  Entry Hour  ...  D1_MACD_deep  D1_MACD_signal RR_Ratio  Target  Main_Target  Target_Buy  Target_Sell  Target_Multiclass
0   2013-07-31 17:00:00       98.335 2013-07-31 17:00:00      98.335       Sell     0.0          2          17  ...           0.0             0.0      inf       0            0          -1            0                  1
1   2013-09-23 09:00:00       99.026 2013-09-23 09:00:00      99.026       Sell     0.0          0           9  ...          -1.0            -1.0      inf       0            0          -1            0                  1
2   2013-09-23 10:00:00       98.947 2013-09-23 19:00:00      98.947       Sell     0.0          0          10  ...          -1.0            -1.0      inf       0            0          -1            0                  1
3   2013-10-01 05:00:00       98.488 2013-10-01 06:00:00      98.488       Sell     0.0          1           5  ...          -1.0            -1.0      inf       0            0          -1            0                  1
4   2014-10-17 18:00:00      106.647 2014-10-17 22:00:00     106.789       Sell  -142.0          4          18  ...          -1.0            -1.0      3.0       0            0          -1            0                  2
..                  ...          ...                 ...         ...        ...     ...        ...         ...  ...           ...             ...      ...     ...          ...         ...          ...                ...
101 2024-11-29 12:00:00      150.154 2024-11-29 12:00:00     150.154       Sell     0.0          4          12  ...          -1.0            -1.0      inf       0            0          -1            0                  1
102 2025-02-11 16:00:00      152.300 2025-02-11 17:00:00     152.522       Sell  -222.0          1          16  ...          -1.0            -1.0      3.0       0            0          -1            0                  2
103 2025-02-11 19:00:00      152.360 2025-02-11 22:00:00     152.609       Sell  -249.0          1          19  ...          -1.0            -1.0      3.0       0            0          -1            0                  2
104 2025-04-18 20:00:00      142.200 2025-04-18 20:00:00     142.200       Sell     0.0          4          20  ...          -1.0            -1.0      inf       0            0          -1            0                  1
105 2025-04-18 21:00:00      142.174 2025-04-21 00:00:00     141.808       Sell   366.0          4          21  ...          -1.0            -1.0      inf       1            1          -1            1                  0

[106 rows x 338 columns]
stats : {'buy': {'win_rate': 0, 'expectancy': 0.0}, 'sell': {'win_rate': 14.29, 'expectancy': -78.238}, 'buy_sell': {'win_rate': 14.29, 'expectancy': -78.238}}
features : 22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon']

✅ แสดงช่วงวันที่และจำนวนวันของแต่ละชุดข้อมูล (train/val/test)
Train: 2013-07-31 ถึง 2021-07-20 (2912 วัน, 63 records)
Val: 2021-09-20 ถึง 2023-05-05 (593 วัน, 21 records)
Test: 2023-07-17 ถึง 2025-04-18 (642 วัน, 22 records)

✅ ข้อมูล df หลังจาก load and process data
จำนวน columns ใน df: 332

✅ ข้อมูล trade_df หลังจาก load and process data
จำนวน columns ใน trade_df: 338
🔍 ตรวจสอบ columns ข้อมูล trade_df ก่อนวิเคราะห์ SL/TP + เวลา : จำนวน 338
['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']

🏗️ เปิดใช้งาน analyze sl tp performance
📊 สถิติการทำงานของ SL/TP:
==================================================
ประเภทการออก        จำนวน     อัตราส่วน 
--------------------------------------------------
TP Hit              6         5.66%
SL Hit              100       94.34%
Technical Exit      0         0.00%
SL + Tech Exit      100       94.34%
==================================================
กำไรเฉลี่ยเมื่อ TP Hit: 653.33
ขาดทุนเฉลี่ยเมื่อ SL Hit: -72.06
อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:9.07

ผลรวม SL + Technical Exit:
- จำนวนเทรดรวม: 100
- อัตราส่วน: 94.34%
- กำไร/ขาดทุนเฉลี่ย: -72.06
- อัตราส่วน Risk/Reward โดยเฉลี่ย: 1:9.07

🏗️ เปิดใช้งาน analyze time performance
📊 ประสิทธิภาพตามวันในสัปดาห์:
          Profit                      Target
           count        mean     sum    mean
DayOfWeek                                   
Monday        10  110.500000  1105.0  0.3000
Tuesday       30  -80.866667 -2426.0  0.0000
Wednesday     25  -66.680000 -1667.0  0.0000
Thursday      16   -4.375000   -70.0  0.0625
Friday        25   -9.120000  -228.0  0.0800

📊 ประสิทธิภาพตามชั่วโมง:
     Profit                        Target
      count        mean     sum      mean
Hour                                     
3         7  -83.857143  -587.0  0.000000
4         3    0.000000     0.0  0.000000
5         2  651.000000  1302.0  0.500000
6         1    0.000000     0.0  0.000000
8         3    0.000000     0.0  0.000000
9         3    0.000000     0.0  0.000000
10       10  -38.300000  -383.0  0.100000
11        3    0.000000     0.0  0.000000
12        2  -77.000000  -154.0  0.000000
13        6  -55.000000  -330.0  0.000000
14        5    0.000000     0.0  0.000000
15       11  -77.454545  -852.0  0.090909
16       13   -1.692308   -22.0  0.076923
17       22  -94.454545 -2078.0  0.045455
18       11  -49.818182  -548.0  0.000000
19        1    0.000000     0.0  0.000000
20        3  122.000000   366.0  0.333333
💾 บันทึกกราฟวิเคราะห์เวลา ที่: LightGBM/Multi/results\M60_USDJPY_time_analysis.png

============================================================
🤖 ขั้นตอนที่ 2: เทรนโมเดล LightGBM
============================================================
🔧 โหมดการทำงาน: Development
🔧 เทรนโมเดลใหม่: ใช่
🔧 ทดสอบ Optimal Parameters: ใช่
============================================================
🔄 ใช้ Multi-Model Architecture (2 โมเดลแยกตามสถานการณ์)

🔄 เทรนโมเดลใหม่ (TRAIN_NEW_MODEL = True)
🔍 Debug: เตรียม merge df และ trade_df
🔍 Debug: df.shape = (74355, 332), trade_df.shape = (106, 338)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_09a_combined_df.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 332
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal']
🚀 ใช้ Logic การ Merge ใหม่โดยอ้างอิงจาก Timestamp ที่ถูกต้อง
🔍 เตรียม Merge ข้อมูล 10 คอลัมน์จาก trade_df
✅ คำนวณ Timeframe จากข้อมูลได้: 0 days 01:00:00
✅ Merge สำเร็จ พบข้อมูลที่ตรงกัน 106 รายการ
✅ จัดการค่าว่างหลังการ Merge...
✅ เติมค่าว่างตามที่กำหนดเรียบร้อยแล้ว
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_09b_combined_df_merge.csv

🔍 ตรวจสอบ columns ข้อมูล combined_df ขั้นตอนเทรน : จำนวน 342
['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_12_26_9', 'MACDs_12_26_9', 'MACDh_12_26_9', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_Upper', 'BB_Lower', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'High_Prev_Max', 'Low_Prev_Min', 'Support_50', 'Resistance_50', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'Volume_MA_20', 'H2_Bar_CL', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'Entry Time', 'Entry Price', 'Exit Price', 'Trade Type', 'Profit', 'Exit Condition', 'Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
features : 22
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon']
✅ ใช้ Final selected feature: 22 feature

🏗️ เปิดใช้งาน train all scenario models
🚀 เริ่มเทรนโมเดลทั้ง 2 scenarios สำหรับ USDJPY_M60
============================================================
📁 ใช้ results_dir: LightGBM/Multi/results
🔍 Debug: USE_MULTICLASS_TARGET = True
🔍 Debug: target_column = Target_Multiclass
✅ สร้างโฟลเดอร์ทั้งหมดเรียบร้อยแล้ว
🔍 Debug: ตรวจสอบคอลัมน์ใน df ก่อนเพิ่ม market_scenario
🔍 Debug: df.columns[:10] = ['Date', 'Time', 'Open', 'High', 'Low', 'Close', 'Volume', 'DateTime', 'DayOfWeek', 'Hour']
🔍 Debug: มีคอลัมน์ Close? True
🔍 Debug: มีคอลัมน์ EMA200? True

🏗️ เปิดใช้งาน add market scenario column

📈 Market Scenario Distribution:
  uptrend: 38308 (51.5%)
  downtrend: 30527 (41.1%)
  sideways: 5520 (7.4%)
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_10a_df_with_scenario.csv

================================================================================
📊 กำลังเทรน trend_following...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ trend_following

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 74355 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 68835/74355 rows (92.6%)
📊 ข้อมูลหลังกรอง trend_following: 68835 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 68764, 1.0: 46, 2.0: 24, 3.0: 1}
⚠️ พบ class ที่มีข้อมูลน้อยเกินไป (ต่ำสุด: 1 samples)
🔧 อาจต้องปรับการแบ่งข้อมูลในขั้นตอนการเทรน
✅ เตรียมข้อมูล trend_following: 68835 samples, 22 features
✅ ข้อมูลพร้อม: X.shape=(68835, 22), y.shape=(68835,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ trend_following

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following สำหรับ USDJPY_M60
📊 ข้อมูล: 68835 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following สำหรับ USDJPY_M60
📊 ข้อมูล: 68835 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 68764, 1.0: 46, 2.0: 24, 3.0: 1}
⚠️ พบ class ที่มีข้อมูลน้อยเกินไป (ต่ำสุด: 1 samples)
🔧 จะใช้การแบ่งข้อมูลแบบไม่ stratify
📈 Train: 41301, Val: 13767, Test: 13767
📊 Train class distribution: {0.0: 41267, 1.0: 23, 2.0: 11}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=35
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=0, nan=0, large_values=2
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=17
⚠️ ทำความสะอาดข้อมูลใน X_test...
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_trend_following_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_trend_following_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: USDJPY, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.33360796762546346, 1.0: 598.5652173913044, 2.0: 1251.5454545454545}
📊 trend_following Parameter Distributions (based on USDJPY_M60):
   learning_rate: 0.039 - 0.089 (base: 0.064)
   num_leaves: 22 - 28 (base: 25)
   max_depth: 4 - 8 (base: 6)
   Strategy: Stability-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following: {'reg_lambda': 0.0, 'reg_alpha': 0.005, 'num_leaves': 22, 'min_data_in_leaf': 10, 'max_depth': 7, 'learning_rate': 0.035, 'feature_fraction': 0.84, 'bagging_freq': 1, 'bagging_fraction': 0.87}
✅ Best CV score สำหรับ trend_following: 0.7125

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
4599       1.050000          700.0       1.0        0.0          0                 -0.015        0  ...     6   -0.115257     0.0                 -0.002559          0             1.0          4
9180       1.120172         4500.0      -1.0       -1.0          0                  0.232        0  ...    13    0.136497    -1.0                 -0.550438          0             0.0          3
33841     -0.503311         7527.0       1.0        1.0          0                  0.297        0  ...    15    0.040492     1.0                  0.042892          0             0.0          4
49730      1.720000         5378.0      -1.0        0.0          0                  0.004        0  ...     6   -0.101068    -1.0                  0.000418          0             1.0          2
47890      2.152174         1566.0      -1.0        0.0          0                  0.029        0  ...    14    0.057925     0.0                 -0.021507          0             0.0          0

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following
📊 Class distribution: {0.0: 41267, 1.0: 23, 2.0: 11}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 41267, 1.0: 23, 2.0: 11}
🔄 Oversampling class 1.0: 23 -> 200 (+177 samples)
🔄 Oversampling class 2.0: 11 -> 200 (+189 samples)
📊 Class distribution หลัง oversample: {0.0: 41267, 1.0: 200, 2.0: 200}
📊 Class distribution หลัง oversample: {0.0: 41267, 1.0: 200, 2.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11a_df_XTrain_trend_following.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11b_df_YTrain_trend_following.csv
❌ เกิดข้อผิดพลาดในการเทรน trend_following: y contains previously unseen labels: [3.0]
❌ เทรน trend_following ล้มเหลว - result เป็น None

================================================================================
📊 กำลังเทรน counter_trend...
================================================================================
🔍 Debug: เตรียมข้อมูลสำหรับ counter_trend

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Multiclass

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 74355 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5520/74355 rows (7.4%)
📊 ข้อมูลหลังกรอง counter_trend: 5520 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5491, 1.0: 17, 2.0: 12}
✅ เตรียมข้อมูล counter_trend: 5520 samples, 22 features
✅ ข้อมูลพร้อม: X.shape=(5520, 22), y.shape=(5520,)
🔍 Debug: เรียกใช้ train scenario model() สำหรับ counter_trend

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend สำหรับ USDJPY_M60
📊 ข้อมูล: 5520 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend สำหรับ USDJPY_M60
📊 ข้อมูล: 5520 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5491, 1.0: 17, 2.0: 12}
📈 Train: 3312, Val: 1104, Test: 1104
📊 Train class distribution: {0.0: 3295, 1.0: 10, 2.0: 7}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=4
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=0, nan=0, large_values=2
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=0
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_counter_trend_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_counter_trend_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: USDJPY, TF: M60)
  🎯 Multi-class Class Weights: {0.0: 0.3350531107738999, 1.0: 110.4, 2.0: 157.71428571428572}
📊 counter_trend Parameter Distributions (based on USDJPY_M60):
   learning_rate: 0.024 - 0.104 (base: 0.064)
   num_leaves: 15 - 35 (base: 25)
   max_depth: 5 - 7 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend: {'reg_lambda': 0.005, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 7, 'max_depth': 8, 'learning_rate': 0.08, 'feature_fraction': 0.88, 'bagging_freq': 3, 'bagging_fraction': 0.8}
✅ Best CV score สำหรับ counter_trend: 0.8850

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
39180      0.569767          486.0      -1.0       -1.0          0                  0.079        1  ...     2    0.020472     0.0                 -0.003122          0             0.0          2
16112      0.220000         3986.0       1.0       -1.0          0                 -0.246        0  ...    13    0.041738    -1.0                  0.015455          0             0.0          4
35276      0.213873         2944.0      -1.0        0.0          1                  0.152        0  ...     9    0.063119     0.0                 -0.001827          0             0.0          4
13622      1.136364         2727.0       1.0        0.0          0                  0.008        0  ...    12    0.020961     0.0                 -0.017167          0            -1.0          3
55432      0.687500         9835.0      -1.0        0.0          0                  0.078        0  ...     6   -0.021405     0.0                 -0.060251          0             1.0          0

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend
📊 Class distribution: {0.0: 3295, 1.0: 10, 2.0: 7}
📊 Imbalance ratio: 0.002
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 3295, 1.0: 10, 2.0: 7}
🔄 Oversampling class 1.0: 10 -> 200 (+190 samples)
🔄 Oversampling class 2.0: 7 -> 200 (+193 samples)
📊 Class distribution หลัง oversample: {0.0: 3295, 1.0: 200, 2.0: 200}
📊 Class distribution หลัง oversample: {0.0: 3295, 1.0: 200, 2.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11a_df_XTrain_counter_trend.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11b_df_YTrain_counter_trend.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[63]	valid_0's multi_logloss: 0.0376692

Accuracy: 0.9918
Classification Report:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00      1098
         1.0       0.00      0.00      0.00         3
         2.0       0.00      0.00      0.00         3

    accuracy                           0.99      1104
   macro avg       0.33      0.33      0.33      1104
weighted avg       0.99      0.99      0.99      1104

Confusion Matrix:
[[1095    1    2]
 [   3    0    0]
 [   3    0    0]]
📊 Test Set Accuracy: 0.9918, F1-Score: 0.9905
🔍 ประเมินผลแบบ Multiclass
✅ คำนวณ Multiclass AUC สำเร็จ: 0.9557
✅ counter_trend - Accuracy: 0.992, F1: 0.990, AUC: 0.956

🔍 ตรวจสอบคุณภาพโมเดล counter_trend...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1104, 22)
   y_val shape: (1104,), unique: [0. 1. 2.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: USDJPY MM60 (counter_trend)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (1104, 22)
   y_val shape: (1104,), unique: [0. 1. 2.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (1104, 3)
   Multi-class classification detected (3 classes)
   Using model.predict() to get actual class values instead of argmax
   Final y_pred shape: (1104,), unique: [0. 1.], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (1104,), unique values: [0. 1. 2.]
   y_pred shape: (1104,), unique values: [0. 1.]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   Multi-class classification metrics calculated
   Calculated metrics: Acc=0.9937, F1=0.9919, Prec=0.9901, Rec=0.9937
✅ ML Metrics:
   Accuracy: 0.9937
   AUC: 0.8908
   F1: 0.9919
   Precision: 0.9901
   Recall: 0.9937
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.57
   Trades: 110

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
✅ โมเดล USDJPY MM60 (counter_trend) ผ่านเกณฑ์คุณภาพ

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
✅ โหลด metrics ก่อนหน้าสำเร็จ: 060_USDJPY_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 16:08:10
📊 โมเดล: USDJPY MM60 (counter_trend)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ดีขึ้น - auc: -0.1020 (-10.27%)
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.1020 (-10.27%)
🏷️ ประเภท: declined
================================================================================


================================================================================
📋 สรุปผลการประเมิน: USDJPY MM60 (counter_trend)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ดีขึ้น - auc: -0.1020 (-10.27%)
🏷️ ประเภท: declined
================================================================================
⏭️ ไม่บันทึกโมเดล counter_trend - โมเดลไม่ดีขึ้น - auc: -0.1020 (-10.27%)
📊 สร้าง Feature Importance สำหรับ counter_trend
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_USDJPY_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_USDJPY_M60 symbol USDJPY timeframe M60 (counter_trend)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
Momentum5_x_Volatility10 0.1674 0.0922
         Volume_Change_1 0.1192 0.0881
                    Hour 0.1048 0.0681
            Bar_longwick 0.1022 0.0978
              ATR_ROC_i2 0.0781 0.0907
         H12_Price_Range 0.0706 0.0478
   MACD_line_x_PriceMove 0.0624 0.0778
               Bar_CL_HL 0.0619 0.0383
           Volume_Lag_10 0.0502 0.0812
          H12_Price_Move 0.0322 0.0416
                  Bar_SW 0.0300 0.0262
                STO_zone 0.0284 0.0141
            Volume_Lag_5 0.0256 0.0637
             Price_Range 0.0234 0.0588
                 IsNight 0.0142 0.0092

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_USDJPY_counter_trend_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_USDJPY_counter_trend_feature_importance.csv (ขนาด: 1225 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
Momentum5_x_Volatility10    0.127470
   MACD_line_x_PriceMove    0.095226
            Bar_longwick    0.079940
         H12_Price_Range    0.077962
           Volume_Lag_10    0.067450
              ATR_ROC_i2    0.066139
          H12_Price_Move    0.064670
           Volume_Lag_30    0.058931
             Price_Range    0.054237
         Volume_Change_1    0.051317
            Volume_Lag_5    0.050974
           Volume_Lag_20    0.043327
                    Hour    0.031859
                STO_zone    0.027063
               Bar_CL_HL    0.023545

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       0.99      1.00      1.00      1098
         1.0       0.00      0.00      0.00         3
         2.0       0.00      0.00      0.00         3

    accuracy                           0.99      1104
   macro avg       0.33      0.33      0.33      1104
weighted avg       0.99      0.99      0.99      1104

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_USDJPY_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend
✅ สร้าง trade_df สำหรับ counter_trend: 1104 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend/M60_USDJPY
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend/M60_USDJPY\M60_USDJPY_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend/M60_USDJPY\M60_USDJPY_evaluation_report.csv
📁 ขนาดไฟล์: 341 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend/M60_USDJPY\M60_USDJPY_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend/M60_USDJPY\M60_USDJPY_performance_curves.png
📁 ขนาดไฟล์: 271438 bytes
✅ เทรน counter_trend สำเร็จ
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_10b_df_with_scenario.csv

📊 กำลังเทรน Target_Buy...

--- Training for Target_Buy ---

📊 กำลังเทรน trend_following สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 74250 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 68762/74250 rows (92.6%)
📊 ข้อมูลหลังกรอง trend_following: 68762 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 68762}
✅ เตรียมข้อมูล trend_following: 68762 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Buy สำหรับ USDJPY_M60
📊 ข้อมูล: 68762 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Buy สำหรับ USDJPY_M60
📊 ข้อมูล: 68762 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน trend_following_Buy ล้มเหลว - result เป็น None

📊 กำลังเทรน counter_trend สำหรับ Target_Buy...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Buy

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 74250 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5488/74250 rows (7.4%)
📊 ข้อมูลหลังกรอง counter_trend: 5488 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5488}
✅ เตรียมข้อมูล counter_trend: 5488 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Buy สำหรับ USDJPY_M60
📊 ข้อมูล: 5488 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Buy สำหรับ USDJPY_M60
📊 ข้อมูล: 5488 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
⚠️ มี class เดียว (1) ไม่สามารถเทรนได้
❌ เทรน counter_trend_Buy ล้มเหลว - result เป็น None

📊 กำลังเทรน Target_Sell...

--- Training for Target_Sell ---

📊 กำลังเทรน trend_following สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ trend_following
🔍 Debug: ข้อมูลเริ่มต้น: 74354 rows
📊 Trend Following Strategy (Buy ใน Uptrend + Sell ใน Downtrend): 68834/74354 rows (92.6%)
📊 ข้อมูลหลังกรอง trend_following: 68834 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ trend_following: {0.0: 68831, 1.0: 3}
✅ เตรียมข้อมูล trend_following: 68834 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล trend_following_Sell สำหรับ USDJPY_M60
📊 ข้อมูล: 68834 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล trend_following_Sell สำหรับ USDJPY_M60
📊 ข้อมูล: 68834 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 68831, 1.0: 3}
📈 Train: 41300, Val: 13767, Test: 13767
📊 Train class distribution: {0.0: 41299, 1.0: 1}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=32
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=0, nan=0, large_values=12
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=10
⚠️ ทำความสะอาดข้อมูลใน X_test...
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ trend_following_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ trend_following_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_trend_following_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_trend_following_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ trend_following_Sell
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 41299, 1.0: 1}
   Imbalance ratio: 41299.0:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 trend_following_Sell Parameter Distributions (based on USDJPY_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ trend_following_Sell: {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 6, 'max_depth': 7, 'learning_rate': 0.02, 'feature_fraction': 0.82, 'bagging_freq': 3, 'bagging_fraction': 0.78}
✅ Best CV score สำหรับ trend_following_Sell: 0.8971

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
47508     -0.326531         1565.0       1.0        1.0          0                 -0.119        0  ...    15    0.101355     1.0                  0.033549          0             0.0          4
21869     -0.988506         1645.0      -1.0        0.0          0                  0.018        1  ...     2   -0.002056     0.0                 -0.075683          0            -1.0          0
34914      4.310345          970.0      -1.0        0.0          1                 -0.005        0  ...     8    0.062092     0.0                 -0.086492          0            -1.0          4
37502      0.966102         1828.0      -1.0        0.0          0                 -0.034        0  ...     4    0.047659     0.0                 -0.024310          0             1.0          2
65139     -0.811189         3876.0       1.0       -1.0          0                 -0.133        1  ...     2   -0.054315     0.0                 -0.036189          0             0.0          3

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ trend_following_Sell
📊 Class distribution: {0.0: 41299, 1.0: 1}
📊 Imbalance ratio: 0.000
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 41299, 1.0: 1}
🔄 Oversampling class 1.0: 1 -> 200 (+199 samples)
📊 Class distribution หลัง oversample: {0.0: 41299, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 41299, 1.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11a_df_XTrain_trend_following_Sell.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11b_df_YTrain_trend_following_Sell.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.0705055

Accuracy: 0.9996
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13766
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00     13767
   macro avg       0.50      0.50      0.50     13767
weighted avg       1.00      1.00      1.00     13767

Confusion Matrix:
[[13762     4]
 [    1     0]]
📊 Test Set Accuracy: 0.9996, F1-Score: 0.9997
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.1565
✅ trend_following_Sell - Accuracy: 1.000, F1: 1.000, AUC: 0.157

🔍 ตรวจสอบคุณภาพโมเดล trend_following_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (13767, 22)
   y_val shape: (13767,), unique: [0. 1.]
   y_val type: <class 'pandas.core.series.Series'>
🏗️ เปิดใช้งาน evaluate and decide model save

================================================================================
🔍 เริ่มการประเมินโมเดล: USDJPY MM60 (trend_following_Sell)
================================================================================
📊 คำนวณ ML Metrics...
🔍 Input Data Check:
   X_val shape: (13767, 22)
   y_val shape: (13767,), unique: [0. 1.], type: <class 'pandas.core.series.Series'>
   y_pred_proba shape: (13767, 2)
   Binary classification detected
   Final y_pred shape: (13767,), unique: [0 1], type: <class 'numpy.ndarray'>
🔍 Debug Metrics Calculation:
   y_val shape: (13767,), unique values: [0. 1.]
   y_pred shape: (13767,), unique values: [0 1]
   y_val sample: [0. 0. 0. 0. 0. 0. 0. 0. 0. 0.]
   y_pred sample: [0 0 0 0 0 0 0 0 0 0]
   Binary classification metrics calculated (labels=[0. 1.])
   Calculated metrics: Acc=0.9999, F1=0.0000, Prec=0.0000, Rec=0.0000
✅ ML Metrics:
   Accuracy: 0.9999
   AUC: 0.6088
   F1: 0.0000
   Precision: 0.0000
   Recall: 0.0000
✅ Trading Stats:
   Win Rate: 65.00%
   Expectancy: 49.98
   Trades: 1376

🔍 ตรวจสอบคุณภาพโมเดล...
🏗️ เปิดใช้งาน validate model performance
❌ โมเดล USDJPY MM60 (trend_following_Sell) ไม่ผ่านเกณฑ์คุณภาพ
📋 เกณฑ์ที่ไม่ผ่าน:
   - F1 Score 0.0000 < 0.1
⚠️ คำเตือนเพิ่มเติม:
   - Precision 0.0000 < 0.1
   - Recall 0.0000 < 0.5

📋 เปรียบเทียบกับโมเดลก่อนหน้า...
🏗️ เปิดใช้งาน load previous model metrics
ℹ️ ไม่พบไฟล์ metrics ก่อนหน้า: 060_USDJPY_metrics.json
🏗️ เปิดใช้งาน compare model with previous

💾 ตัดสินใจบันทึกโมเดล...
🏗️ เปิดใช้งาน should save model
🏗️ เปิดใช้งาน send model alert

================================================================================
⚠️ MODEL ALERT - WARNING
================================================================================
🕒 เวลา: 2025-09-14 16:08:49
📊 โมเดล: USDJPY MM60 (trend_following_Sell)
📋 รายละเอียด: โมเดลไม่ผ่านการประเมิน - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
💾 การบันทึก: ❌ ไม่บันทึก
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================


================================================================================
📋 สรุปผลการประเมิน: USDJPY MM60 (trend_following_Sell)
================================================================================
💾 บันทึกโมเดล: ❌ ไม่
📝 เหตุผล: โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
🏷️ ประเภท: rejected
================================================================================
⏭️ ไม่บันทึกโมเดล trend_following_Sell - โมเดลไม่ผ่านเกณฑ์คุณภาพขั้นต่ำ: F1 Score 0.0000 < 0.1
📊 สร้าง Feature Importance สำหรับ trend_following_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = trend_following_Sell_USDJPY_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ trend_following_Sell_USDJPY_M60 symbol USDJPY timeframe M60 (trend_following_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
Momentum5_x_Volatility10 0.8044 0.4692
            Bar_longwick 0.1310 0.2815
            Volume_Lag_5 0.0634 0.0557
               DayOfWeek 0.0006 0.0205
          Price_Strangth 0.0006 0.0205
   MACD_line_x_PriceMove 0.0000 0.0557
             Price_Range 0.0000 0.0176
           Volume_Lag_30 0.0000 0.0323
                    Hour 0.0000 0.0029
          H12_Price_Move 0.0000 0.0176
           Volume_Lag_10 0.0000 0.0117
              ATR_ROC_i2 0.0000 0.0059
         H12_Price_Range 0.0000 0.0059
           Volume_Lag_20 0.0000 0.0029
                  Bar_SW 0.0000 0.0000

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_USDJPY_trend_following_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_USDJPY_trend_following_Sell_feature_importance.csv (ขนาด: 981 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
Momentum5_x_Volatility10    0.266018
            Bar_longwick    0.112449
              ATR_ROC_i2    0.087083
         Volume_Change_1    0.084538
            Volume_Lag_5    0.072392
          H12_Price_Move    0.067516
           Volume_Lag_10    0.058470
         H12_Price_Range    0.046436
             Price_Range    0.046329
           Volume_Lag_30    0.034689
   MACD_line_x_PriceMove    0.033295
               DayOfWeek    0.031000
               IsEvening    0.025378
           Volume_Lag_20    0.012253
          Price_Strangth    0.010160

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00     13766
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00     13767
   macro avg       0.50      0.50      0.50     13767
weighted avg       1.00      1.00      1.00     13767

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_USDJPY_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ trend_following_Sell
✅ สร้าง trade_df สำหรับ trend_following_Sell: 13767 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ trend_following_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/trend_following_Sell/M60_USDJPY
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/trend_following_Sell/M60_USDJPY\M60_USDJPY_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/trend_following_Sell/M60_USDJPY\M60_USDJPY_evaluation_report.csv
📁 ขนาดไฟล์: 365 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ trend_following_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ trend_following_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/trend_following_Sell/M60_USDJPY\M60_USDJPY_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/trend_following_Sell/M60_USDJPY\M60_USDJPY_performance_curves.png
📁 ขนาดไฟล์: 269541 bytes
✅ เทรน trend_following_Sell สำเร็จ

📊 กำลังเทรน counter_trend สำหรับ Target_Sell...

🏗️ เปิดใช้งาน prepare scenario data
🎯 ใช้ target column: Target_Sell

🏗️ เปิดใช้งาน filter data by scenario
🔍 Debug: กรองข้อมูลสำหรับ counter_trend
🔍 Debug: ข้อมูลเริ่มต้น: 74354 rows
📊 Counter Trend Strategy (Mean Reversion ใน Weak Trend/Sideways): 5520/74354 rows (7.4%)
📊 ข้อมูลหลังกรอง counter_trend: 5520 samples

🛠️ Features used for training (Selected: 22 total):
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
📊 Class distribution สำหรับ counter_trend: {0.0: 5517, 1.0: 3}
✅ เตรียมข้อมูล counter_trend: 5520 samples, 22 features

🏗️ เปิดใช้งาน train scenario model
🔧 เทรนโมเดล counter_trend_Sell สำหรับ USDJPY_M60
📊 ข้อมูล: 5520 samples, 22 features
📁 results_dir: LightGBM/Multi/results

🔧 เทรนโมเดล counter_trend_Sell สำหรับ USDJPY_M60
📊 ข้อมูล: 5520 samples, 22 features
🔍 Debug: ตรวจสอบ NaN ใน y
🔍 Debug: จำนวน NaN ใน y: 0
🔍 Debug: จำนวน NaN ใน X: 0
🔍 Class distribution: {0.0: 5517, 1.0: 3}
📈 Train: 3312, Val: 1104, Test: 1104
📊 Train class distribution: {0.0: 3310, 1.0: 2}
🔍 ตรวจสอบข้อมูลก่อน scaling...
📊 X_train: inf=0, nan=0, large_values=4
⚠️ ทำความสะอาดข้อมูลใน X_train...
📊 X_val: inf=0, nan=0, large_values=1
⚠️ ทำความสะอาดข้อมูลใน X_val...
📊 X_test: inf=0, nan=0, large_values=1
⚠️ ทำความสะอาดข้อมูลใน X_test...
✅ ข้อมูลสะอาดแล้ว
⚙️ ข้าม Feature Scaling สำหรับ counter_trend_Sell (LightGBM ไม่จำเป็นต้อง scale)

🔍 ตรวจสอบสถานะ Hyperparameter Tuning สำหรับ counter_trend_Sell:
   do_hyperparameter_tuning = False
   flag_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_counter_trend_Sell_tuning_flag.json
   param_file = LightGBM/Hyper_Multi/M60_USDJPY\M60_USDJPY_counter_trend_Sell_best_params.json

📖 โหลด best parameters ที่บันทึกไว้สำหรับ counter_trend_Sell
🏗️ เปิดใช้งาน get scenario param distributions

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 2
  📊 Classes: [0. 1.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: False

🏗️ เปิดใช้งาน get optimal class weight
   Class distribution: {0.0: 3310, 1.0: 2}
   Imbalance ratio: 1655.0:1
   Enhanced class weight: {0.0: 1, 1.0: 15}
  🎯 Auto Class Weight: {0.0: 1, 1.0: 15}
📊 counter_trend_Sell Parameter Distributions (based on USDJPY_M60):
   learning_rate: 0.027 - 0.097 (base: 0.062)
   num_leaves: 18 - 34 (base: 26)
   max_depth: 4 - 8 (base: 6)
   Strategy: Flexibility-focused
✅ โหลด parameters สำเร็จสำหรับ counter_trend_Sell: {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 8, 'max_depth': 5, 'learning_rate': 0.02, 'feature_fraction': 0.86, 'bagging_freq': 3, 'bagging_fraction': 0.9}
✅ Best CV score สำหรับ counter_trend_Sell: 0.7978

--- Features (Columns) ---
['Bar_longwick', 'Volume_Lag_20', 'STO_zone', 'Bar_CL_HL', 'IsMorning', 'MACD_line_x_PriceMove', 'IsNight', 'H12_Price_Range', 'H12_Price_Move', 'IsAfternoon', 'Volume_Lag_5', 'Volume_Change_1', 'Volume_Lag_10', 'Volume_Lag_30', 'Price_Range', 'Hour', 'ATR_ROC_i2', 'Bar_SW', 'Momentum5_x_Volatility10', 'IsEvening', 'Price_Strangth', 'DayOfWeek']
--- Sample Data (First 5 rows) ---
       Bar_longwick  Volume_Lag_20  STO_zone  Bar_CL_HL  IsMorning  MACD_line_x_PriceMove  IsNight  ...  Hour  ATR_ROC_i2  Bar_SW  Momentum5_x_Volatility10  IsEvening  Price_Strangth  DayOfWeek
29268      0.548077         1723.0      -1.0       -1.0          0                  0.102        1  ...    22    0.103748    -1.0                 -0.049904          0             1.0          0
56092      0.234694         5306.0       1.0        0.0          0                 -0.176        0  ...    18    0.025925     0.0                  0.366632          1             1.0          2
36803      0.518519         1559.0       1.0        0.0          0                 -0.027        1  ...     1    0.024862     0.0                 -0.001992          0             1.0          3
9343      -0.252252         3274.0       1.0        1.0          1                 -0.260        0  ...     8    0.012090     1.0                  0.023978          0             0.0          0
54963      0.321678          890.0       1.0       -1.0          0                 -0.103        0  ...    17   -0.085304     0.0                  0.079144          1             0.0          0

[5 rows x 22 columns]

🔍 ตรวจสอบ Class Imbalance สำหรับ counter_trend_Sell
📊 Class distribution: {0.0: 3310, 1.0: 2}
📊 Imbalance ratio: 0.001
🔄 ใช้ oversampling เนื่องจาก imbalance รุนแรง
🏗️ เปิดใช้งาน oversample minority
📊 Class distribution ก่อน oversample: {0.0: 3310, 1.0: 2}
🔄 Oversampling class 1.0: 2 -> 200 (+198 samples)
📊 Class distribution หลัง oversample: {0.0: 3310, 1.0: 200}
📊 Class distribution หลัง oversample: {0.0: 3310, 1.0: 200}
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11a_df_XTrain_counter_trend_Sell.csv
✅ Saved cleaned file to: LightGBM/Data_Trained\M60_USDJPY_11b_df_YTrain_counter_trend_Sell.csv
Training until validation scores don't improve for 50 rounds
Did not meet early stopping. Best iteration is:
[100]	valid_0's binary_logloss: 0.074382

Accuracy: 0.9991
Classification Report:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1103
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00      1104
   macro avg       0.50      0.50      0.50      1104
weighted avg       1.00      1.00      1.00      1104

Confusion Matrix:
[[1103    0]
 [   1    0]]
📊 Test Set Accuracy: 0.9991, F1-Score: 0.9986
🔍 ประเมินผลแบบ Binary
✅ คำนวณ Binary AUC สำเร็จ: 0.6133
✅ counter_trend_Sell - Accuracy: 0.999, F1: 0.999, AUC: 0.613

🔍 ตรวจสอบคุณภาพโมเดล counter_trend_Sell...
🔍 Pre-evaluation Data Check:
   X_val_scaled shape: (1104, 22)
   y_val shape: (1104,), unique: [0.]
   y_val type: <class 'pandas.core.series.Series'>
⚠️ y_val มี class เดียว - ข้ามการประเมินคุณภาพ
✅ บันทึกโมเดล counter_trend_Sell ที่: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_trained.pkl
✅ บันทึก features counter_trend_Sell ที่: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_features.pkl
✅ บันทึก scaler counter_trend_Sell ที่: LightGBM/Multi/models\counter_trend_Sell\M60_USDJPY_scaler.pkl
📊 สร้าง Feature Importance สำหรับ counter_trend_Sell
🔍 Debug: results folder = LightGBM/Multi/results
🔍 Debug: model name = counter_trend_Sell_USDJPY_M60

🏗️ เปิดใช้งาน plot feature importance
-> กำลังสร้าง Feature Importance สำหรับ counter_trend_Sell_USDJPY_M60 symbol USDJPY timeframe M60 (counter_trend_Sell)

📊 Feature Importance (Normalized Scores - Gain and Split) - Top 15:
                 Feature   Gain  Split
          Price_Strangth 0.5546 0.1951
              ATR_ROC_i2 0.2655 0.1726
   MACD_line_x_PriceMove 0.0976 0.1895
             Price_Range 0.0549 0.1126
         Volume_Change_1 0.0107 0.1351
            Bar_longwick 0.0080 0.0732
            Volume_Lag_5 0.0045 0.0169
Momentum5_x_Volatility10 0.0033 0.0169
           Volume_Lag_20 0.0006 0.0056
           Volume_Lag_10 0.0003 0.0300
           Volume_Lag_30 0.0000 0.0469
          H12_Price_Move 0.0000 0.0038
         H12_Price_Range 0.0000 0.0019
                 IsNight 0.0000 0.0000
             IsAfternoon 0.0000 0.0000

💾 บันทึก Feature Importance ละเอียดที่: LightGBM/Multi/results\M60_USDJPY_counter_trend_Sell_feature_importance.csv
✅ ยืนยันการบันทึกไฟล์สำเร็จ: LightGBM/Multi/results\M60_USDJPY_counter_trend_Sell_feature_importance.csv (ขนาด: 926 bytes)
🌲 สร้าง Random Forest Feature Importance สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน test random forest
🔍 กำลังทดสอบ RandomForest...

📊 RandomForest Feature Importance - Top 15:
                 Feature  Importance
              ATR_ROC_i2    0.143338
   MACD_line_x_PriceMove    0.142100
           Volume_Lag_10    0.107904
          Price_Strangth    0.107792
             Price_Range    0.077637
            Bar_longwick    0.068122
Momentum5_x_Volatility10    0.054792
           Volume_Lag_20    0.050304
         H12_Price_Range    0.047484
            Volume_Lag_5    0.042986
          H12_Price_Move    0.037450
         Volume_Change_1    0.029458
               DayOfWeek    0.026147
           Volume_Lag_30    0.025441
               IsMorning    0.012379

📈 RandomForest Performance:
              precision    recall  f1-score   support

         0.0       1.00      1.00      1.00      1103
         1.0       0.00      0.00      0.00         1

    accuracy                           1.00      1104
   macro avg       0.50      0.50      0.50      1104
weighted avg       1.00      1.00      1.00      1104

💾 บันทึก RandomForest importance ที่: LightGBM/Multi/results\M60_USDJPY_random_forest_feature_importance.csv

🏗️ เปิดใช้งาน compare feature importance
💾 บันทึกกราฟเปรียบเทียบ Feature Importance ที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.png
💾 บันทึกตารางเปรียบเทียบที่: LightGBM/Multi/results\M60_USDJPY_feature_importance_comparison.csv
✅ เปรียบเทียบ Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง Feature Importance สำเร็จสำหรับ counter_trend_Sell
✅ สร้าง trade_df สำหรับ counter_trend_Sell: 1104 rows

🏗️ เปิดใช้งาน create scenario evaluation report
🔍 Debug: สร้าง evaluation report สำหรับ counter_trend_Sell
🔍 Debug: result keys: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug: สร้างโฟลเดอร์ LightGBM/Multi/results/counter_trend_Sell/M60_USDJPY
🔍 Debug: กำลังบันทึก evaluation report ที่ LightGBM/Multi/results/counter_trend_Sell/M60_USDJPY\M60_USDJPY_evaluation_report.csv
💾 บันทึก Evaluation Report: LightGBM/Multi/results/counter_trend_Sell/M60_USDJPY\M60_USDJPY_evaluation_report.csv
📁 ขนาดไฟล์: 356 bytes
🔍 Debug: สร้าง performance curves plot สำหรับ counter_trend_Sell

🏗️ เปิดใช้งาน create performance curves plot
🔍 Debug: เริ่มสร้าง performance curves plot สำหรับ counter_trend_Sell
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/counter_trend_Sell/M60_USDJPY\M60_USDJPY_performance_curves.png
💾 บันทึก Performance Curves Plot: LightGBM/Multi/results/counter_trend_Sell/M60_USDJPY\M60_USDJPY_performance_curves.png
📁 ขนาดไฟล์: 271732 bytes
✅ เทรน counter_trend_Sell สำเร็จ

✅ เทรนเสร็จสิ้น: 3 โมเดล
🔍 Debug: ผลลัพธ์การเทรน:
  ✅ counter_trend: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ trend_following_Sell: มีผลลัพธ์
    📊 Feature Importance: True
  ✅ counter_trend_Sell: มีผลลัพธ์
    📊 Feature Importance: True

📊 บันทึก Random Forest Feature Importance

🏗️ เปิดใช้งาน save combined random forest importance
📊 รวบรวม RF Feature Importance จาก counter_trend
📊 รวบรวม RF Feature Importance จาก trend_following_Sell
📊 รวบรวม RF Feature Importance จาก counter_trend_Sell
🔍 Debug: คอลัมน์ใน combined_rf_df: ['Feature', 'Importance', 'Scenario']
💾 บันทึก Random Forest Feature Importance: LightGBM/Multi/results\M60_USDJPY_random_forest_feature_importance.csv
📊 รวมจาก 3 scenarios, 22 features

🏆 Top 10 Random Forest Features:
  Momentum5_x_Volatility10: Importance=0.1494
  ATR_ROC_i2: Importance=0.0989
  MACD_line_x_PriceMove: Importance=0.0902
  Bar_longwick: Importance=0.0868
  Volume_Lag_10: Importance=0.0779
  Price_Range: Importance=0.0594
  H12_Price_Range: Importance=0.0573
  H12_Price_Move: Importance=0.0565
  Volume_Lag_5: Importance=0.0555
  Volume_Change_1: Importance=0.0551

📊 สร้าง Combined Feature Importance จากทุก scenarios

🏗️ เปิดใช้งาน create combined feature importance
📊 รวบรวม Feature Importance จาก counter_trend
📊 รวบรวม Feature Importance จาก trend_following_Sell
📊 รวบรวม Feature Importance จาก counter_trend_Sell
💾 บันทึก Combined Feature Importance: LightGBM/Multi/results/M60\M60_USDJPY_feature_importance.csv
📊 รวมจาก 3 scenarios, 22 features

🏆 Top 10 Features (Combined):
  Momentum5_x_Volatility10: Gain=0.3250, Split=0.1928, Count=3
  Price_Strangth: Gain=0.1857, Split=0.0741, Count=3
  ATR_ROC_i2: Gain=0.1145, Split=0.0897, Count=3
  Bar_longwick: Gain=0.0804, Split=0.1508, Count=3
  MACD_line_x_PriceMove: Gain=0.0533, Split=0.1077, Count=3
  Volume_Change_1: Gain=0.0433, Split=0.0744, Count=3
  Hour: Gain=0.0349, Split=0.0237, Count=3
  Volume_Lag_5: Gain=0.0312, Split=0.0454, Count=3
  Price_Range: Gain=0.0261, Split=0.0630, Count=3
  H12_Price_Range: Gain=0.0235, Split=0.0185, Count=3

🔍 ทำ Cross-Validation และ Threshold Optimization สำหรับ Multi-Model
📊 ทำ Time Series Cross-Validation...

🏗️ เปิดใช้งาน time series cv
🔁 เริ่มทำ Time Series Cross-Validation (n_splits=5, original=5)
📊 CV Configuration: splits=5, test_size=10622, total_samples=74355

📊 Fold 1/5:
  - Train size: 21245 ตัวอย่าง (28.6% ของข้อมูลทั้งหมด)
  - Val size:   10622 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9989644622264062, 1.0: 0.000564838785596611, 2.0: 0.0004706989879971758}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 3
  📊 Classes: [0. 1. 2.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 3 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.3336788704078908, 1.0: 590.1388888888889, 2.0: 708.1666666666666}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 94 trees)
  📊 ผลลัพธ์ Fold 1:
    - Accuracy:  0.9986
    - AUC:       0.8643
    - F1 Score:  0.9981
    - Precision: 0.9982
    - Recall:    0.9986

📊 Fold 2/5:
  - Train size: 31867 ตัวอย่าง (42.9% ของข้อมูลทั้งหมด)
  - Val size:   10622 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9988389242790348, 2.0: 0.0006276084978190605, 1.0: 0.0005334672231462014}
⚠️ เตือน : ใน Fold 2 พบคลาสใน validation ที่ไม่มีใน training: {3.0}
  - Training classes: [0.0, 1.0, 2.0]
  - Validation classes: [0.0, 1.0, 2.0, 3.0]
⚠️ ข้าม Fold 2 เนื่องจากมีคลาสที่ไม่เคยเห็นใน training

📊 Fold 3/5:
  - Train size: 42489 ตัวอย่าง (57.1% ของข้อมูลทั้งหมด)
  - Val size:   10622 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9988467603379698, 1.0: 0.0005648520793617172, 2.0: 0.0005648520793617172, 3.0: 2.3535503306738213e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.25028864278982094, 1.0: 442.59375, 2.0: 442.59375, 3.0: 10622.25}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 113 trees)
  📊 ผลลัพธ์ Fold 3:
    - Accuracy:  0.9983
    - AUC:       0.5000
    - F1 Score:  0.9979
    - Precision: 0.9976
    - Recall:    0.9983

📊 Fold 4/5:
  - Train size: 53111 ตัวอย่าง (71.4% ของข้อมูลทั้งหมด)
  - Val size:   10622 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.9988326335410743, 1.0: 0.000640168703281806, 2.0: 0.000508369264370846, 3.0: 1.8828491272994294e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.2502921826990141, 1.0: 390.5220588235294, 2.0: 491.76851851851853, 3.0: 13277.75}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 121 trees)
  📊 ผลลัพธ์ Fold 4:
    - Accuracy:  0.9976
    - AUC:       0.5000
    - F1 Score:  0.9967
    - Precision: 0.9958
    - Recall:    0.9976

📊 Fold 5/5:
  - Train size: 63733 ตัวอย่าง (85.7% ของข้อมูลทั้งหมด)
  - Val size:   10622 ตัวอย่าง
  - การกระจายคลาสใน Train: {0.0: 0.998666311016271, 1.0: 0.0008159038488695025, 2.0: 0.0005020946762273862, 3.0: 1.5690458632105818e-05}
🏗️ กำลังสร้างโมเดล...

🏗️ เปิดใช้งาน get lgbm params
  📊 จำนวน unique classes: 4
  📊 Classes: [0. 1. 2. 3.]
  🎯 USE_MULTICLASS_TARGET: True
  🎯 Is Multi-class: True
  🎯 Multi-class Classification: 4 classes
  🎯 Adaptive Parameters: lr=0.0637, leaves=25 (Symbol: UNKNOWN, TF: 60)
  🎯 Multi-class Class Weights: {0.0: 0.25033386752136755, 1.0: 306.40865384615387, 2.0: 497.9140625, 3.0: 15933.25}
  ✅ สร้างโมเดลสำเร็จ (ใช้ 187 trees)
  📊 ผลลัพธ์ Fold 5:
    - Accuracy:  0.9983
    - AUC:       0.5000
    - F1 Score:  0.9977
    - Precision: 0.9972
    - Recall:    0.9983

📊 สรุปการทำ Time Series CV:
   - จำนวน folds ที่วางแผน: 5
   - จำนวน folds ที่สำเร็จ: 4

✅ การทำ Time Series Cross-Validation เสร็จสมบูรณ์
📌 ผลลัพธ์เฉลี่ย:
  - Accuracy:  0.9982
  - AUC:       0.5911
  - F1 Score:  0.9976
  - Precision: 0.9972
  - Recall:    0.9982
✅ Time Series CV เสร็จสิ้น: AUC=0.591, F1=0.998
🔍 Debug: กำลังบันทึก CV results ที่ LightGBM/Multi/results\multi_scenario_cv_results.json
💾 บันทึก CV Results: LightGBM/Multi/results\multi_scenario_cv_results.json
📁 ขนาดไฟล์: 166 bytes

📊 สร้าง Performance Analysis และ Comparison Plots

🏗️ เปิดใช้งาน create multi scenario performance analysis
🔍 Debug: กำลังบันทึกไฟล์ที่ LightGBM/Multi/results\multi_scenario_performance_analysis.txt
💾 บันทึก Multi-Scenario Performance Analysis: LightGBM/Multi/results\multi_scenario_performance_analysis.txt
📁 ขนาดไฟล์: 1228 bytes

🏗️ เปิดใช้งาน create performance comparison plots
🔍 Debug: สร้างโฟลเดอร์ plots ที่ LightGBM/Multi/results/plots
🔍 Debug: จำนวน scenarios: 3
🔍 Debug: scenarios: ['counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug: metrics data: {'AUC': [0.9556982124287539, 0.1565087897719018, 0.613327289211242], 'F1_Score': [0.990494691262827, 0.9997457490782325, 0.9986415095578627], 'Accuracy': [0.9918478260869565, 0.9996368126679741, 0.9990942028985508]}
🔍 Debug: สร้างกราฟสำหรับ AUC
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_auc_comparison.png
💾 บันทึก AUC Comparison Plot: LightGBM/Multi/results/plots\performance_auc_comparison.png
📁 ขนาดไฟล์: 125292 bytes
🔍 Debug: สร้างกราฟสำหรับ F1_Score
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_f1_score_comparison.png
💾 บันทึก F1_Score Comparison Plot: LightGBM/Multi/results/plots\performance_f1_score_comparison.png
📁 ขนาดไฟล์: 128369 bytes
🔍 Debug: สร้างกราฟสำหรับ Accuracy
🔍 Debug: กำลังบันทึกกราฟที่ LightGBM/Multi/results/plots\performance_accuracy_comparison.png
💾 บันทึก Accuracy Comparison Plot: LightGBM/Multi/results/plots\performance_accuracy_comparison.png
📁 ขนาดไฟล์: 131036 bytes
🔍 Debug: สร้างกราฟรวมทุก metrics
🔍 Debug: กำลังบันทึกกราฟรวมที่ LightGBM/Multi/results/plots\performance_combined_comparison.png
💾 บันทึก Combined Performance Comparison Plot: LightGBM/Multi/results/plots\performance_combined_comparison.png
📁 ขนาดไฟล์: 190614 bytes

🏗️ เปิดใช้งาน create final and training results
💾 บันทึก Final Results: LightGBM/Multi/results/counter_trend\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/counter_trend\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/counter_trend\M60_USDJPY_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/counter_trend\M60_USDJPY_feature_importance_comparison.png
💾 บันทึก Final Results: LightGBM/Multi/results/trend_following\final_results.csv
💾 บันทึก Training Results: LightGBM/Multi/results/trend_following\training_results.csv

🏗️ เปิดใช้งาน create group feature importance comparison
💾 บันทึก Feature Importance Comparison: LightGBM/Multi/results/trend_following\M60_USDJPY_feature_importance_comparison.csv

🏗️ เปิดใช้งาน create feature importance comparison plot
💾 บันทึก Feature Importance Comparison Plot: LightGBM/Multi/results/trend_following\M60_USDJPY_feature_importance_comparison.png

🏗️ เริ่มบันทึกผลลัพธ์ลงระบบสรุป Multi-Model
🔍 Debug: จำนวน results = 3
🔍 Debug: ตรวจสอบ counter_trend
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1104
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend
   📊 Test stats: {'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 28.66}, 'sell': {'count': 1102, 'win_rate': 60.25, 'expectancy': 4.32}, 'buy_sell': {'count': 1104, 'win_rate': 60.33, 'expectancy': 4.36}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9918478260869565, 'auc': 0.9556982124287539, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=USDJPY, timeframe=M60, scenario=counter_trend
🔍 Debug: train_val_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 28.66}, 'sell': {'count': 1102, 'win_rate': 60.25, 'expectancy': 4.32}, 'buy_sell': {'count': 1104, 'win_rate': 60.33, 'expectancy': 4.36}}
🔍 Debug: test_stats={'buy': {'count': 2, 'win_rate': 100.0, 'expectancy': 28.66}, 'sell': {'count': 1102, 'win_rate': 60.25, 'expectancy': 4.32}, 'buy_sell': {'count': 1104, 'win_rate': 60.33, 'expectancy': 4.36}}
🔍 Debug: model_metrics={'accuracy': 0.9918478260869565, 'auc': 0.9556982124287539, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 76.9/100
📈 Win Rate เฉลี่ย: 61.7%, Expectancy เฉลี่ย: 4.70
🏆 โมเดลที่ดีที่สุด: USDJPY M060 (Score: 77.2)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน USDJPY MM60 เรียบร้อย
📊 Performance Score: 77.22
✅ บันทึกสรุปสำหรับ counter_trend เรียบร้อย
🔍 Debug: ตรวจสอบ trend_following_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 13767
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ trend_following_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}, 'buy_sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9996368126679741, 'auc': 0.1565087897719018, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=USDJPY, timeframe=M60, scenario=trend_following_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}, 'buy_sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}, 'buy_sell': {'count': 13767, 'win_rate': 62.61, 'expectancy': 4.84}}
🔍 Debug: model_metrics={'accuracy': 0.9996368126679741, 'auc': 0.1565087897719018, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 74.5/100
📈 Win Rate เฉลี่ย: 62.9%, Expectancy เฉลี่ย: 4.94
🏆 โมเดลที่ดีที่สุด: GOLD M060 (Score: 76.5)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน USDJPY MM60 เรียบร้อย
📊 Performance Score: 72.45
✅ บันทึกสรุปสำหรับ trend_following_Sell เรียบร้อย
🔍 Debug: ตรวจสอบ counter_trend_Sell
   - result is not None: True
   - keys in result: ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
   - 'trade_df' in result: True
   - trade_df is not None: True
   - trade_df length: 1104
   - trade_df columns: ['Date', 'Trade Type', 'Profit', 'Entry Price', 'Exit Price', 'Prediction', 'Confidence']
✅ กำลังบันทึกสรุปสำหรับ counter_trend_Sell
   📊 Test stats: {'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}, 'buy_sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}}
   ⚙️ Training config: {'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}
   🤖 Model metrics: {'accuracy': 0.9990942028985508, 'auc': 0.613327289211242, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}

🏗️ เปิดใช้งาน save training results to summary
🔍 Debug: symbol=USDJPY, timeframe=M60, scenario=counter_trend_Sell
🔍 Debug: train_val_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}, 'buy_sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}}
🔍 Debug: test_stats={'buy': {'count': 0, 'win_rate': 0.0, 'expectancy': 0.0}, 'sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}, 'buy_sell': {'count': 1104, 'win_rate': 61.59, 'expectancy': 4.55}}
🔍 Debug: model_metrics={'accuracy': 0.9990942028985508, 'auc': 0.613327289211242, 'f1': 0.0, 'precision': 0.0, 'recall': 0.0}
🔍 Debug: training_config={'threshold': 0.5, 'nbars_sl': 5, 'num_features': 22, 'hyperparameter_tuning': False, 'use_smote': False}

🏗️ เปิดใช้งาน create training summary system
🔍 Debug: กำลังสร้างโฟลเดอร์: LightGBM/Multi/training_summaries
✅ สร้างโฟลเดอร์สำเร็จ: LightGBM/Multi/training_summaries
🔍 Debug: summary_folder created at: LightGBM/Multi/training_summaries

🏗️ เปิดใช้งาน create training progress report

============================================================
🌟 สรุปภาพรวมระบบการเทรน
============================================================
📊 จำนวนโมเดล: 2, คะแนนเฉลี่ย: 76.7/100
📈 Win Rate เฉลี่ย: 62.3%, Expectancy เฉลี่ย: 4.79
🏆 โมเดลที่ดีที่สุด: USDJPY M060 (Score: 76.9)
============================================================
✅ สร้างรายงานความก้าวหน้าเรียบร้อย
✅ บันทึกผลการเทรน USDJPY MM60 เรียบร้อย
📊 Performance Score: 76.89
✅ บันทึกสรุปสำหรับ counter_trend_Sell เรียบร้อย
📊 บันทึกสรุปเสร็จสิ้น: 3/3 scenarios
✅ เทรนโมเดลสำเร็จ: 3 scenarios
🔍 Debug Multi-Model: จำนวน scenarios = 3
🔍 Debug Scenario 'counter_trend': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend' metrics: {'accuracy': 0.9918478260869565, 'f1_score': 0.990494691262827, 'auc': 0.9556982124287539, 'train_samples': 3695, 'test_samples': 1104, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'counter_trend' cv_results: {'best_score': 0.884963768115942, 'best_params': {'reg_lambda': 0.005, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 7, 'max_depth': 8, 'learning_rate': 0.08, 'feature_fraction': 0.88, 'bagging_freq': 3, 'bagging_fraction': 0.8}, 'scenario': 'counter_trend'}
🔍 Debug Scenario 'trend_following_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'trend_following_Sell' metrics: {'accuracy': 0.9996368126679741, 'f1_score': 0.9997457490782325, 'auc': 0.1565087897719018, 'train_samples': 41499, 'test_samples': 13767, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'trend_following_Sell' cv_results: {'best_score': 0.8970655374155468, 'best_params': {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 6, 'max_depth': 7, 'learning_rate': 0.02, 'feature_fraction': 0.82, 'bagging_freq': 3, 'bagging_fraction': 0.78}, 'scenario': 'trend_following_Sell'}
🔍 Debug Scenario 'counter_trend_Sell': keys = ['scenario', 'symbol', 'timeframe', 'model', 'scaler', 'features', 'accuracy', 'f1_score', 'auc', 'model_path', 'feature_path', 'scaler_path', 'train_samples', 'test_samples', 'feature_importance', 'rf_importance', 'trade_df', 'metrics', 'cv_results']
🔍 Debug Scenario 'counter_trend_Sell' metrics: {'accuracy': 0.9990942028985508, 'f1_score': 0.9986415095578627, 'auc': 0.613327289211242, 'train_samples': 3510, 'test_samples': 1104, 'scenario': 'counter_trend_Sell'}
🔍 Debug Scenario 'counter_trend_Sell' cv_results: {'best_score': 0.7978391186354149, 'best_params': {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 8, 'max_depth': 5, 'learning_rate': 0.02, 'feature_fraction': 0.86, 'bagging_freq': 3, 'bagging_fraction': 0.9}, 'scenario': 'counter_trend_Sell'}
🔍 Debug avg_metrics['accuracy'] = 0.9969 (จาก 3 scenarios)
🔍 Debug avg_metrics['f1_score'] = 0.9963 (จาก 3 scenarios)
🔍 Debug avg_metrics['auc'] = 0.5752 (จาก 3 scenarios)
🔍 Debug avg_metrics['train_samples'] = 16234.6667 (จาก 3 scenarios)
🔍 Debug avg_metrics['test_samples'] = 5325.0000 (จาก 3 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug avg_cv_results['best_score'] = 0.8600 (จาก 3 scenarios)
⚠️ ไม่มีค่าตัวเลขสำหรับ best_params: [{'reg_lambda': 0.005, 'reg_alpha': 0.0, 'num_leaves': 42, 'min_data_in_leaf': 7, 'max_depth': 8, 'learning_rate': 0.08, 'feature_fraction': 0.88, 'bagging_freq': 3, 'bagging_fraction': 0.8}, {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 25, 'min_data_in_leaf': 6, 'max_depth': 7, 'learning_rate': 0.02, 'feature_fraction': 0.82, 'bagging_freq': 3, 'bagging_fraction': 0.78}, {'reg_lambda': 0.02, 'reg_alpha': 0.005, 'num_leaves': 20, 'min_data_in_leaf': 8, 'max_depth': 5, 'learning_rate': 0.02, 'feature_fraction': 0.86, 'bagging_freq': 3, 'bagging_fraction': 0.9}]
⚠️ ไม่มีค่าตัวเลขสำหรับ scenario: ['counter_trend', 'trend_following_Sell', 'counter_trend_Sell']
🔍 Debug Final avg_metrics: {'accuracy': 0.9968596138844937, 'f1_score': 0.9962939832996408, 'auc': 0.5751780971372992, 'train_samples': 16234.666666666666, 'test_samples': 5325.0}
🔍 Debug Final avg_cv_results: {'best_score': 0.8599561413889679}

==================================================
🎯 เริ่มทดสอบ Optimal Parameters
==================================================
🔍 Debug validation data:
   - X_val shape: (21, 22)
   - X_val index range: 63 - 83
   - combined_df shape: (74355, 342)
   - combined_df index range: 0 - 74354
✅ พบ indices ที่ตรงกัน: 21 จาก 21
📊 ข้อมูล validation สำหรับ optimization:
   - จำนวน samples: 21
   - จำนวน features: 22
   - Index range: 63 - 83
✅ ข้อมูล validation เพียงพอสำหรับการทดสอบ (21 samples)

💾 การบันทึก Artifacts สำหรับการทดสอบแบบ Offline...
✅ บันทึก Models Artifact สำเร็จที่: LightGBM/Data_Trained\M60_USDJPY_scenario_results.pkl
✅ บันทึก Validation Set Artifact สำเร็จที่: LightGBM/Data_Trained\M60_USDJPY_validation_set.csv
⚠️ ไม่พบโมเดล trend_following_Buy: LightGBM/Multi/models/trend_following_Buy/M60_USDJPY_trained.pkl
⚠️ ไม่พบโมเดล trend_following_Sell: LightGBM/Multi/models/trend_following_Sell/M60_USDJPY_trained.pkl
📋 จะทดสอบ 4 scenarios: ['trend_following', 'counter_trend', 'counter_trend_Buy', 'counter_trend_Sell']

📌 Running scenario: trend_following
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_USDJPY_trend_following_optimal_threshold.pkl

📌 Running scenario: counter_trend
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_USDJPY_counter_trend_optimal_threshold.pkl

📌 Running scenario: counter_trend_Buy
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_USDJPY_counter_trend_Buy_optimal_threshold.pkl

📌 Running scenario: counter_trend_Sell
🏗️ เปิดใช้งาน find optimal threshold multi model
เทียบค่า threshold : inf / 0.6500-0.3000
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
💾 Saved: LightGBM/Multi/thresholds/M60_USDJPY_counter_trend_Sell_optimal_threshold.pkl

🔍 ทดสอบการเรียกใช้งาน threshold
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
   Threshold  Precision  Recall   F1
0   0.000815        0.0     0.0  0.0
1   0.001878        0.0     0.0  0.0
2   0.002940        0.0     0.0  0.0
3   0.004003        0.0     0.0  0.0
4   0.005066        0.0     0.0  0.0
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500
Best threshold: 0.65
Metrics: {'Accuracy': 1.0, 'Precision': 0.0, 'Recall': 0.0, 'F1': 0.0, 'ROC_AUC': nan}
   Threshold  Precision  Recall   F1
0   0.000068        0.0     0.0  0.0
1   0.000440        0.0     0.0  0.0
2   0.000812        0.0     0.0  0.0
3   0.001185        0.0     0.0  0.0
4   0.001557        0.0     0.0  0.0

🏗️ เปิดใช้งาน load optimal threshold (backward compatibility)
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
threshold (backward compatibility) : threshold 0.65 default 0.3

✅ symbol USDJPY M60 : threshold 0.65 : trend 0.6500 counter 0.6500

🎯 ทดสอบ Optimal nBars SL...
🏗️ เปิดใช้งาน find optimal nbars sl multi model

======================================================================
🎯 เริ่มการทดสอบ Optimal nBars_SL สำหรับ USDJPY MM60
======================================================================
📊 ข้อมูล validation: 21 samples
🔧 จำนวน scenarios: 3
🎯 nBars_SL เริ่มต้น: 4

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend: 5
📋 nBars_SL เดิม: 5
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        0.000    0.200      -2.00        20.0                 
3        0.000    0.200      -2.00        20.0                 
4        0.000    0.200      -2.00        20.0                 
5        0.500    0.500      3.00         12.5       🏆 BEST    
6        0.500    0.500      3.00         12.5                 
7        0.500    0.500      3.00         12.5                 
8        0.500    0.500      3.00         12.5                 
9        0.500    0.500      3.00         12.5                 
10       0.500    0.500      3.00         12.5                 
11       0.500    0.500      3.00         12.5                 
12       0.500    0.500      3.00         12.5                 
13       0.500    0.500      3.00         12.5                 
14       0.500    0.500      3.00         12.5                 
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 5
   🔸 nBars_SL ใหม่: 5
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.5000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 5 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend: 5
📁 ไฟล์: M60_USDJPY_counter_trend_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend: 5

🔍 ทดสอบ Optimal nBars_SL สำหรับ trend_following_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ trend_following_Sell: 5
📋 nBars_SL เดิม: 5
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ trend_following_Sell:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        0.000    0.200      -2.00        20.0                 
3        0.000    0.200      -2.00        20.0                 
4        0.000    0.200      -2.00        20.0                 
5        0.500    0.500      3.00         12.5       🏆 BEST    
6        0.500    0.500      3.00         12.5                 
7        0.500    0.500      3.00         12.5                 
8        0.500    0.500      3.00         12.5                 
9        0.500    0.500      3.00         12.5                 
10       0.500    0.500      3.00         12.5                 
11       0.500    0.500      3.00         12.5                 
12       0.500    0.500      3.00         12.5                 
13       0.500    0.500      3.00         12.5                 
14       0.500    0.500      3.00         12.5                 
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 5
   🔸 nBars_SL ใหม่: 5
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.5000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 5 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ trend_following_Sell: 5
📁 ไฟล์: M60_USDJPY_trend_following_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ trend_following_Sell: 5

🔍 ทดสอบ Optimal nBars_SL สำหรับ counter_trend_Sell
──────────────────────────────────────────────────
🏗️ เปิดใช้งาน load scenario nbars
✅ โหลด nBars_SL สำหรับ counter_trend_Sell: 5
📋 nBars_SL เดิม: 5
🧪 ทดสอบ nBars_SL values: [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]

📊 ผลการทดสอบ nBars_SL สำหรับ counter_trend_Sell:
nBars_SL Score    Win Rate   Expectancy   Max DD     Status    
──────────────────────────────────────────────────────────────────────
2        0.000    0.200      -2.00        20.0                 
3        0.000    0.200      -2.00        20.0                 
4        0.000    0.200      -2.00        20.0                 
5        0.500    0.500      3.00         12.5       🏆 BEST    
6        0.500    0.500      3.00         12.5                 
7        0.500    0.500      3.00         12.5                 
8        0.500    0.500      3.00         12.5                 
9        0.500    0.500      3.00         12.5                 
10       0.500    0.500      3.00         12.5                 
11       0.500    0.500      3.00         12.5                 
12       0.500    0.500      3.00         12.5                 
13       0.500    0.500      3.00         12.5                 
14       0.500    0.500      3.00         12.5                 
15       0.500    0.500      3.00         12.5                 

📈 การเปรียบเทียบ:
   🔸 nBars_SL เดิม: 5
   🔸 nBars_SL ใหม่: 5
   🔸 การเปลี่ยนแปลง: +0 bars
   🔸 Score ที่ดีที่สุด: 0.5000
   ➡️ ไม่เปลี่ยนแปลง - ค่าเดิมยังเหมาะสมที่สุด
   🔄 Counter Trend: nBars_SL = 5 เหมาะสำหรับการเทรด reversal
   ⚡ SL ใกล้ - เหมาะกับ quick reversal, cut loss เร็ว
🏗️ เปิดใช้งาน save scenario nbars
✅ บันทึก nBars_SL สำหรับ counter_trend_Sell: 5
📁 ไฟล์: M60_USDJPY_counter_trend_Sell_optimal_nBars_SL.pkl
✅ บันทึก Optimal nBars_SL สำหรับ counter_trend_Sell: 5

======================================================================
📋 สรุปผลการทดสอบ Optimal nBars_SL สำหรับ USDJPY MM60
======================================================================
🔸 counter_trend:
   nBars_SL: 5 → 5 (+0 bars)
   Best Score: 0.5000
🔸 trend_following_Sell:
   nBars_SL: 5 → 5 (+0 bars)
   Best Score: 0.5000
🔸 counter_trend_Sell:
   nBars_SL: 5 → 5 (+0 bars)
   Best Score: 0.5000

💡 คำแนะนำการใช้งาน:
   ⚡ Counter Trend (5 bars): สมดุล, เหมาะกับ swing reversal
   ⚡ Counter Trend (5 bars): สมดุล, เหมาะกับ swing reversal
   ⚡ Counter Trend (5 bars): สมดุล, เหมาะกับ swing reversal

✅ การทดสอบ Optimal nBars_SL เสร็จสิ้น
💾 บันทึกผลลัพธ์แล้ว: 3 scenarios
✅ ผลการทดสอบ Optimal nBars SL:
   - counter_trend: 5
   - trend_following_Sell: 5
   - counter_trend_Sell: 5
✅ ทดสอบ Optimal Parameters เสร็จสิ้น

✅ ข้อมูล df และ trade_df หลังจาก train and evaluate
จำนวน columns ใน df: 332
จำนวน columns ใน trade_df: 338

[INFO] จำนวน Features หลัง train and evaluate (fallback): 22
🔍 Debug ไฟล์ CSV_Files_Fixed/USDJPY_H1_FIXED.csv:
   metrics: {'accuracy': 0.9968596138844937, 'f1_score': 0.9962939832996408, 'auc': 0.5751780971372992, 'train_samples': 16234.666666666666, 'test_samples': 5325.0}
   cv_results: {'best_score': 0.8599561413889679}

📊 เปรียบเทียบผลลัพธ์:
| Metric      | CV Avg    | Test Set |
|-------------|-----------|----------|
| Accuracy    | 0.0000    | 0.9969 |
| AUC         | 0.5000    | 0.5752 |
| F1 Score    | 0.0000    | 0.0000 |

🔍 ตรวจสอบเงื่อนไข Performance Tracking:
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
   training_success: True
   เงื่อนไขรวม: True
✅ เข้าเงื่อนไข Performance Tracking - จะบันทึกไฟล์
🔍 Debug metrics from result_dict: {'accuracy': 0.9968596138844937, 'f1_score': 0.9962939832996408, 'auc': 0.5751780971372992, 'train_samples': 16234.666666666666, 'test_samples': 5325.0}
🔍 Debug cv_results from result_dict: {'best_score': 0.8599561413889679}
🔍 Final model_metrics: {'avg_accuracy': 0.9968596138844937, 'avg_f1_score': 0.5, 'avg_auc': 0.5751780971372992, 'total_train_samples': 63, 'total_test_samples': 22}
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ trend_following: 0.6500
🏗️ เปิดใช้งาน load scenario threshold
✅ โหลด threshold สำหรับ counter_trend: 0.6500

🏗️ เปิดใช้งาน load time filters
กำลังโหลด time filters จาก: LightGBM/Multi/thresholds/M60_USDJPY_time_filters.pkl
✅ โหลด time filters สำเร็จ (M60_USDJPY)
🔍 Debug loaded time_filters: {'days': [], 'hours': [], 'detailed_stats': {'days': {'Monday': {'win_rate': 0.0, 'expectancy': 214.33333333333357, 'total_trades': 3.0, 'day_index': 0, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.3333333333333333}, 'Tuesday': {'win_rate': 0.0, 'expectancy': -156.99999999999173, 'total_trades': 3.0, 'day_index': 1, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.6666666666666666}, 'Wednesday': {'win_rate': 0.0, 'expectancy': -38.12500000000085, 'total_trades': 8.0, 'day_index': 2, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.125}, 'Thursday': {'win_rate': 0.0, 'expectancy': 407.99999999999653, 'total_trades': 3.0, 'day_index': 3, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.3333333333333333}, 'Friday': {'win_rate': 0.0, 'expectancy': 73.20000000000277, 'total_trades': 5.0, 'day_index': 4, 'strong_buy_rate': 0.0, 'weak_buy_rate': 0.0, 'no_trade_rate': 0.0}}, 'hours': {3: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 5: {'win_rate': 0.0, 'expectancy': 1431.0000000000116, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 8: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 10: {'win_rate': 0.0, 'expectancy': -305.0000000000068, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 11: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 2.0, 'strong_buy_rate': 0.0}, 14: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 15: {'win_rate': 0.0, 'expectancy': -55.499999999995, 'total_trades': 4.0, 'strong_buy_rate': 0.0}, 17: {'win_rate': 0.0, 'expectancy': 87.19999999999573, 'total_trades': 5.0, 'strong_buy_rate': 0.0}, 18: {'win_rate': 0.0, 'expectancy': -82.99999999999841, 'total_trades': 3.0, 'strong_buy_rate': 0.0}, 19: {'win_rate': 0.0, 'expectancy': 0.0, 'total_trades': 1.0, 'strong_buy_rate': 0.0}, 20: {'win_rate': 0.0, 'expectancy': 366.00000000001387, 'total_trades': 1.0, 'strong_buy_rate': 0.0}}}}
   ⚠️ time_filters ว่าง - สร้าง default
   ✅ สร้าง default time_filters: {'days': [0, 1, 2, 3, 4], 'hours': [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], 'source': 'default'}

🎯 กำลังเรียกใช้ record_model_performance...

🏗️ เปิดใช้งาน record model performance
   Symbol: USDJPY, Timeframe: M60
   ENABLE_PERFORMANCE_TRACKING: True
   TRACKING_AVAILABLE: True
🔍 Debug trade_df:
   Shape: (106, 338)
   Columns: ['Entry Time', 'Entry Price', 'Exit Time', 'Exit Price', 'Trade Type', 'Profit', 'Entry Day', 'Entry Hour', 'SL Price', 'TP Price', 'Exit Condition', 'Risk', 'Reward', 'ATR at Entry', 'BB Width at Entry', 'RSI14 at Entry', 'Pct_Risk', 'Pct_Reward', 'Volume MA20 at Entry', 'Volume Spike at Entry', 'DayOfWeek', 'Hour', 'IsMorning', 'IsAfternoon', 'IsEvening', 'IsNight', 'Bar_CL', 'Bar_CL_OC', 'Bar_CL_HL', 'Bar_SW', 'Bar_TL', 'Bar_DTB', 'Bar_OSB', 'Bar_FVG', 'Bar_longwick', 'Price_Range', 'Price_Move', 'Price_Strangth', 'Volume_MA20', 'Volume_Spike', 'Volume_Momentum', 'Volume_TrendStrength', 'EMA50', 'EMA100', 'EMA200', 'EMA_Cross_EMA5', 'EMA_Cross_EMA10', 'EMA_Cross_EMA15', 'Price_EMA50', 'EMA_diff_50_100', 'EMA_diff_50_200', 'EMA_diff_100_200', 'Slope_EMA50', 'Slope_EMA100', 'Slope_EMA200', 'MA_Cross_50_100', 'MA_Cross_50_200', 'MA_Cross_100_200', 'Dist_EMA50', 'Dist_EMA100', 'Dist_EMA200', 'Rolling_Close_5', 'Rolling_Close_15', 'RSI14', 'RSI_Zone', 'RSI_trend', 'RSI_counter', 'RSI_signal_EMA4', 'RSI_signal_EMA8', 'RSI_signal_EMA12', 'RSI_Overbought', 'RSI_Oversold', 'RSI_ROC_i2', 'RSI_ROC_i4', 'RSI_ROC_i6', 'RSI_ROC_i8', 'RSI_Divergence_i2', 'RSI_Divergence_i4', 'RSI_Divergence_i6', 'MACD_line', 'MACD_deep', 'MACD_signal', 'STO_cross', 'STO_zone', 'STO_overbought', 'STO_Oversold', 'ADX_Deep', 'ADX_zone_25', 'ADX_zone_15', 'ADX_cross', 'ATR_Deep', 'ATR_ROC_i2', 'ATR_ROC_i4', 'ATR_ROC_i6', 'ATR_ROC_i8', 'BB_width', 'BB_Break_HL', 'BB_Break_CL', 'BB_Outside', 'PullBack_50_Up', 'PullBack_50_Down', 'PullBack_100_Up', 'PullBack_100_Down', 'Ratio_Buy', 'Ratio_Sell', 'RSI_x_VolumeSpike', 'EMA_diff_x_ATR', 'Momentum5_x_Volatility10', 'RSI14_x_BBwidth', 'MACD_signal_x_ADX', 'Price_EMA50_x_RSI_trend', 'RSI14_x_ATR', 'RSI14_x_PriceMove', 'EMA50_x_RollingVol5', 'EMA_diff_x_BBwidth', 'ADX_14_x_ATR', 'MACD_signal_x_RSI14', 'RSI14_x_StochK', 'RSI14_x_StochD', 'MACD_line_x_PriceMove', 'ADX_14_x_RollingVol15', 'RSI14_x_Volume', 'ATR_x_PriceRange', 'RSI14_x_PullBack_50_Up', 'RSI14_x_PullBack_50_Down', 'EMA_diff_x_RSI14', 'ADX_14_x_BBwidth', 'Close_Lag_1', 'Close_Lag_2', 'Close_Lag_3', 'Close_Lag_5', 'Close_Lag_10', 'Close_Lag_15', 'Close_Lag_20', 'Close_Lag_30', 'Close_Lag_50', 'Open_Lag_1', 'Open_Lag_2', 'Open_Lag_3', 'Open_Lag_5', 'Open_Lag_10', 'Open_Lag_15', 'Open_Lag_20', 'Open_Lag_30', 'Open_Lag_50', 'High_Lag_1', 'High_Lag_2', 'High_Lag_3', 'High_Lag_5', 'High_Lag_10', 'High_Lag_15', 'High_Lag_20', 'High_Lag_30', 'High_Lag_50', 'Low_Lag_1', 'Low_Lag_2', 'Low_Lag_3', 'Low_Lag_5', 'Low_Lag_10', 'Low_Lag_15', 'Low_Lag_20', 'Low_Lag_30', 'Low_Lag_50', 'Volume_Lag_1', 'Volume_Lag_2', 'Volume_Lag_3', 'Volume_Lag_5', 'Volume_Lag_10', 'Volume_Lag_15', 'Volume_Lag_20', 'Volume_Lag_30', 'Volume_Lag_50', 'RSI14_Lag_1', 'RSI14_Lag_2', 'RSI14_Lag_3', 'RSI14_Lag_5', 'EMA50_Lag_1', 'EMA50_Lag_2', 'EMA50_Lag_3', 'EMA50_Lag_5', 'EMA100_Lag_1', 'EMA100_Lag_2', 'EMA100_Lag_3', 'EMA100_Lag_5', 'EMA200_Lag_1', 'EMA200_Lag_2', 'EMA200_Lag_3', 'EMA200_Lag_5', 'ATR_Lag_1', 'ATR_Lag_2', 'ATR_Lag_3', 'ATR_Lag_5', 'BB_width_Lag_1', 'BB_width_Lag_2', 'BB_width_Lag_3', 'BB_width_Lag_5', 'MACD_12_26_9_Lag_1', 'MACD_12_26_9_Lag_2', 'MACD_12_26_9_Lag_3', 'MACD_12_26_9_Lag_5', 'MACDs_12_26_9_Lag_1', 'MACDs_12_26_9_Lag_2', 'MACDs_12_26_9_Lag_3', 'MACDs_12_26_9_Lag_5', 'STOCHk_14_3_3_Lag_1', 'STOCHk_14_3_3_Lag_2', 'STOCHk_14_3_3_Lag_3', 'STOCHk_14_3_3_Lag_5', 'STOCHd_14_3_3_Lag_1', 'STOCHd_14_3_3_Lag_2', 'STOCHd_14_3_3_Lag_3', 'STOCHd_14_3_3_Lag_5', 'ADX_14_Lag_1', 'ADX_14_Lag_2', 'ADX_14_Lag_3', 'ADX_14_Lag_5', 'DMP_14_Lag_1', 'DMP_14_Lag_2', 'DMP_14_Lag_3', 'DMP_14_Lag_5', 'DMN_14_Lag_1', 'DMN_14_Lag_2', 'DMN_14_Lag_3', 'DMN_14_Lag_5', 'Close_Return_1', 'Volume_Change_1', 'Close_Return_2', 'Volume_Change_2', 'Close_Return_3', 'Volume_Change_3', 'Close_Return_5', 'Volume_Change_5', 'Close_MA_3', 'Close_Std_3', 'Volume_MA_3', 'Close_MA_5', 'Close_Std_5', 'Volume_MA_5', 'Close_MA_10', 'Close_Std_10', 'Volume_MA_10', 'Close_MA_20', 'Close_Std_20', 'H2_Bar_CL_OC', 'H2_Bar_CL_HL', 'H2_Bar_SW', 'H2_Bar_TL', 'H2_Bar_DTB', 'H2_Bar_OSB', 'H2_Bar_FVG', 'H2_Bar_longwick', 'H2_Price_Range', 'H2_Price_Move', 'H2_Price_Strangth', 'H2_Volume_Spike', 'H2_Volume_Momentum', 'H2_Volume_TrendStrength', 'H2_MACD_line', 'H2_MACD_deep', 'H2_MACD_signal', 'H4_Bar_CL', 'H4_Bar_CL_OC', 'H4_Bar_CL_HL', 'H4_Bar_SW', 'H4_Bar_TL', 'H4_Bar_DTB', 'H4_Bar_OSB', 'H4_Bar_FVG', 'H4_Bar_longwick', 'H4_Price_Range', 'H4_Price_Move', 'H4_Price_Strangth', 'H4_Volume_Spike', 'H4_Volume_Momentum', 'H4_Volume_TrendStrength', 'H4_MACD_line', 'H4_MACD_deep', 'H4_MACD_signal', 'H8_Bar_CL', 'H8_Bar_CL_OC', 'H8_Bar_CL_HL', 'H8_Bar_SW', 'H8_Bar_TL', 'H8_Bar_DTB', 'H8_Bar_OSB', 'H8_Bar_FVG', 'H8_Bar_longwick', 'H8_Price_Range', 'H8_Price_Move', 'H8_Price_Strangth', 'H8_Volume_Spike', 'H8_Volume_Momentum', 'H8_Volume_TrendStrength', 'H8_MACD_line', 'H8_MACD_deep', 'H8_MACD_signal', 'H12_Bar_CL', 'H12_Bar_CL_OC', 'H12_Bar_CL_HL', 'H12_Bar_SW', 'H12_Bar_TL', 'H12_Bar_DTB', 'H12_Bar_OSB', 'H12_Bar_FVG', 'H12_Bar_longwick', 'H12_Price_Range', 'H12_Price_Move', 'H12_Price_Strangth', 'H12_Volume_Spike', 'H12_Volume_Momentum', 'H12_Volume_TrendStrength', 'H12_MACD_line', 'H12_MACD_deep', 'H12_MACD_signal', 'D1_Bar_CL', 'D1_Bar_CL_OC', 'D1_Bar_CL_HL', 'D1_Bar_SW', 'D1_Bar_TL', 'D1_Bar_DTB', 'D1_Bar_OSB', 'D1_Bar_FVG', 'D1_Bar_longwick', 'D1_Price_Range', 'D1_Price_Move', 'D1_Price_Strangth', 'D1_Volume_Spike', 'D1_Volume_Momentum', 'D1_Volume_TrendStrength', 'D1_MACD_line', 'D1_MACD_deep', 'D1_MACD_signal', 'RR_Ratio', 'Target', 'Main_Target', 'Target_Buy', 'Target_Sell', 'Target_Multiclass']
   ใช้ Trade Type แทน Signal
   Trade Type counts: {'Sell': 105, 'Buy': 1}
🔍 calculate_trade_metrics: input shape = (1, 338)
   📊 Total trades: 1
   💰 Winning trades: 0/1
   💰 Total profit: 0.00
   ✅ Calculated metrics: {'count': 1, 'win_rate': 0.0, 'expectancy': 0.0, 'trade_accuracy': 0.0, 'trade_f1_score': 0, 'trade_auc': 0.5}
🔍 calculate_trade_metrics: input shape = (105, 338)
   📊 Total trades: 105
   💰 Winning trades: 6/105
   💰 Total profit: -3286.00
   ✅ Calculated metrics: {'count': 105, 'win_rate': 5.714285714285714, 'expectancy': -31.29523809523892, 'trade_accuracy': 0.05714285714285714, 'trade_f1_score': 0.06857142857142856, 'trade_auc': 0.5228571428571429}

🏗️ เปิดใช้งาน format time filters display
🔍 Debug _compare_with_previous:
   Key: USDJPY_M60
   Current F1: 0.5
   Current AUC: 0.5751780971372992
   Previous F1: 0.5
   Previous AUC: 0.583230976695634
   Summary length: 3
⚠️ ⚠️ โมเดลไม่ดีขึ้น! F1 เปลี่ยน 0.0000, AUC เปลี่ยน -0.0081
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🚨 โมเดลไม่ดีขึ้น - พิจารณาไม่บันทึกโมเดลนี้
🔍 Debug: กำลังเตรียมข้อมูลสำหรับบันทึกผลลัพธ์การเปรียบเทียบ (Multi-Model)
   - Symbol: USDJPY, Timeframe: M60
   - Buy_sell stats (calculated): {'win_rate': 0.05660377358490566, 'expectancy': -31.000000000000814, 'profit_factor': 0.5439911185123453, 'count': 106, 'max_drawdown': inf}
   - Performance data prepared: {'symbol': 'USDJPY', 'timeframe': 'M60', 'entry_config': 'config_1_enhanced_signal', 'entry_config_name': 'Enhanced MACD Signal', 'entry_config_description': 'ใช้ macd_signal พร้อมเงื่อนไข volume และ pullback เพิ่มเติม', 'win_rate': 0.05660377358490566, 'expectancy': -31.000000000000814, 'profit_factor': 0.5439911185123453, 'num_trades': 106, 'max_drawdown': inf, 'model_accuracy': 0.9968596138844937, 'model_auc': 0.5751780971372992, 'model_f1': 0, 'training_date': '2025-09-14T16:09:24.643585', 'training_type': 'multi_model', 'num_features': 22}

🏗️ เปิดใช้งาน save entry config performance

🏗️ เปิดใช้งาน get entry config results folder

🏗️ เปิดใช้งาน get entry config folder name
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\models\M60_USDJPY
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results
✅ สร้างหรือมีอยู่แล้ว: LightGBM/Entry_config_1_enhanced_signal\results\M60_USDJPY
path บันทึกไฟล์ performance_summary.json LightGBM/Entry_config_1_enhanced_signal\results\M60_USDJPY
✅ บันทึกผลการประเมิน config_1_enhanced_signal สำหรับ USDJPY MM60 ที่: LightGBM/Entry_config_1_enhanced_signal\results\M60_USDJPY\performance_summary.json
   📁 ขนาดไฟล์: 760 bytes
✅ บันทึกผลลัพธ์การเปรียบเทียบ Entry Config เสร็จสิ้น (Multi-Model)

✅ เสร็จสิ้นการประมวลผลกลุ่ม M60
📊 ประมวลผล: 2 ไฟล์
📈 ผลลัพธ์: 2 รายการ

================================================================================
🔍 การวิเคราะห์เกณฑ์คุณภาพโมเดล (จาก main function)
================================================================================

================================================================================
📊 การวิเคราะห์เกณฑ์คุณภาพโมเดล (MODEL_QUALITY_THRESHOLDS)
================================================================================
Metric          Current    Recommended  New        Change     Data Points 
--------------------------------------------------------------------------------
Accuracy        0.500      0.994        0.994      +98.8%     5           
AUC             0.500      0.868        0.868      +73.6%     4           
F1 Score        0.100      0.994        0.994      +894.1%    3           
Precision       0.100      0.993        0.993      +892.9%    3           
Recall          0.500      0.994        0.994      +98.8%     3           
Win Rate        30.0%      0.7%         0.7%       -97.8%     5           
Expectancy      25.000     49.717       49.717     +98.9%     5           
Min Trades      10.000    N/A          10.000    N/A        0           

💡 คำแนะนำ:
   - เกณฑ์ใหม่อิงจาก percentile 25% ของข้อมูลจริง
   - ค่าที่แนะนำจะไม่เข้มงวดเกินไปและสมจริงกว่า
   - สามารถปรับ THRESHOLD_PERCENTILE เพื่อเปลี่ยนความเข้มงวด
================================================================================
💾 บันทึกผลการวิเคราะห์เกณฑ์ที่: LightGBM/Multi/threshold_analysis.json
✅ การวิเคราะห์เกณฑ์เสร็จสิ้น

================================================================================
📊 สรุปการปรับ Threshold
================================================================================
🔧 GOLD_M60:
   Original Threshold: 0.650
   Adjusted Threshold: 0.650
   Trades Found: 85
   Reason: No trades with original threshold (Multi-Model)

🔧 USDJPY_M60:
   Original Threshold: 0.650
   Adjusted Threshold: 0.650
   Trades Found: 106
   Reason: No trades with original threshold (Multi-Model)

================================================================================
🔍 Debug: round_results = <class 'dict'>
✅ กลุ่ม M60 สำเร็จ
📊 ผลลัพธ์กลุ่ม M60: สำเร็จ 2, ผิดพลาด 0
⏱️ เวลาที่ใช้: 792.0 วินาที (13.2 นาที)
📈 เฉลี่ยต่อไฟล์: 396.0 วินาที/ไฟล์

────────────────────────────────────────────────────────────
📋 สรุปรอบที่ 1:
   ⏱️ เวลาที่ใช้: 792.0 วินาที (13.2 นาที)
   ✅ ไฟล์สำเร็จ: 2
   ❌ ไฟล์ผิดพลาด: 0
   ⏰ เวลาสิ้นสุด: 16:09:25
   📊 M60: 792.0s (396.0s/ไฟล์)

================================================================================
📊 เริ่มขั้นตอนวิเคราะห์ Feature Importance ข้าม Assets
================================================================================
🔍 เงื่อนไข: TRAIN_NEW_MODEL = True, มีผลลัพธ์ = 1 รายการ

📋 วิเคราะห์ Feature Importance สำหรับกลุ่ม M60
────────────────────────────────────────────────────────────
📂 ค้นหาไฟล์ Feature Importance ใน: LightGBM/Multi\results\M60
💾 จะบันทึกผลลัพธ์ที่: LightGBM/Multi\feature_importance\M60_must_have_features.pkl
🔍 Debug: ไฟล์ทั้งหมดในโฟลเดอร์: 2 ไฟล์
   📄 M60_GOLD_feature_importance.csv
   📄 M60_USDJPY_feature_importance.csv
🔍 Debug: ไฟล์ที่ตรงเงื่อนไข: 2 ไฟล์
✅ พบไฟล์ Feature Importance: 2 ไฟล์
   - M60_GOLD_feature_importance.csv
   - M60_USDJPY_feature_importance.csv
🏗️ เปิดใช้งาน analyze cross asset feature importance
📊 วิเคราะห์ Feature Importance จาก 2 assets
📁 ค้นหาไฟล์ใน: LightGBM/Multi\results\M60
📋 พบไฟล์ feature importance: 2 ไฟล์
🔍 Debug: Columns ในไฟล์ M60_GOLD_feature_importance.csv: ['Feature', 'Gain', 'Split', 'Scenarios_Count']
✅ ใช้ columns: Feature และ Gain
✅ โหลดจาก M60_GOLD_feature_importance.csv: 20 features (กรองแล้ว 0 features)
🔍 Debug: Columns ในไฟล์ M60_USDJPY_feature_importance.csv: ['Feature', 'Gain', 'Split', 'Scenarios_Count']
✅ ใช้ columns: Feature และ Gain
✅ โหลดจาก M60_USDJPY_feature_importance.csv: 20 features (กรองแล้ว 0 features)
📊 รวบรวมข้อมูลจาก 2 assets
📈 พบ features ทั้งหมด: 21 features (หลังกรอง data leakage)
🚫 Features ที่ถูกกรอง: Data Leakage (11) + Raw Price Data (12)
🔍 สถิติการปรากฏของ features:
   - Momentum5_x_Volatility10: ปรากฏใน 2 assets, avg importance: 0.2424
   - Bar_longwick: ปรากฏใน 2 assets, avg importance: 0.1190
   - Volume_Lag_10: ปรากฏใน 2 assets, avg importance: 0.0815
   - Volume_Change_1: ปรากฏใน 2 assets, avg importance: 0.0794
   - H12_Price_Move: ปรากฏใน 2 assets, avg importance: 0.0474
   - Bar_CL_HL: ปรากฏใน 2 assets, avg importance: 0.0398
   - Volume_Lag_20: ปรากฏใน 2 assets, avg importance: 0.0196
   - ATR_ROC_i2: ปรากฏใน 2 assets, avg importance: 0.0745
   - Volume_Lag_30: ปรากฏใน 2 assets, avg importance: 0.0176
   - MACD_line_x_PriceMove: ปรากฏใน 2 assets, avg importance: 0.0412

🎯 เงื่อนไขการเลือก features:
   - Top 20 features ต่อ asset
   - ปรากฏในอย่างน้อย 1 assets
   - เลือก 20 features สุดท้าย

📊 ผลการวิเคราะห์:
   🔍 Total unique features: 21
   ✅ Features ที่ผ่านเกณฑ์: 21
   🧹 Features หลังกรอง data leakage: 21
   🎯 Selected features: 20

🏆 Top 20 Features (สะอาด):
   1. Momentum5_x_Volatility10 (avg: 0.2424, assets: 2)
   2. Bar_longwick (avg: 0.1190, assets: 2)
   3. Price_Strangth (avg: 0.0949, assets: 2)
   4. Volume_Lag_10 (avg: 0.0815, assets: 2)
   5. Volume_Change_1 (avg: 0.0794, assets: 2)
   6. ATR_ROC_i2 (avg: 0.0745, assets: 2)
   7. H12_Price_Move (avg: 0.0474, assets: 2)
   8. MACD_line_x_PriceMove (avg: 0.0412, assets: 2)
   9. Bar_CL_HL (avg: 0.0398, assets: 2)
   10. Volume_Lag_5 (avg: 0.0298, assets: 2)
   11. Price_Range (avg: 0.0269, assets: 2)
   12. Hour (avg: 0.0258, assets: 2)
   13. Volume_Lag_20 (avg: 0.0196, assets: 2)
   14. H12_Price_Range (avg: 0.0183, assets: 2)
   15. Volume_Lag_30 (avg: 0.0176, assets: 2)
   16. Bar_SW (avg: 0.0170, assets: 2)
   17. STO_zone (avg: 0.0086, assets: 2)
   18. DayOfWeek (avg: 0.0079, assets: 2)
   19. IsNight (avg: 0.0059, assets: 2)
   20. IsEvening (avg: 0.0043, assets: 1)
🔍 ตรวจสอบสุดท้าย: 20 features สะอาด
📋 Features ที่จะบันทึก: ['Momentum5_x_Volatility10', 'Bar_longwick', 'Price_Strangth', 'Volume_Lag_10', 'Volume_Change_1']...
✅ บันทึกไฟล์: LightGBM/Multi\feature_importance\M60_must_have_features.pkl
✅ วิเคราะห์ Feature Importance สำหรับ M60 เสร็จสิ้น
📊 Features ที่ได้: 20 features
🎯 Top 10 Features:
    1. Momentum5_x_Volatility10
    2. Bar_longwick
    3. Price_Strangth
    4. Volume_Lag_10
    5. Volume_Change_1
    6. ATR_ROC_i2
    7. H12_Price_Move
    8. MACD_line_x_PriceMove
    9. Bar_CL_HL
   10. Volume_Lag_5
   ... และอีก 10 features

================================================================================
✅ การวิเคราะห์ Feature Importance ข้าม Assets เสร็จสิ้น
================================================================================

================================================================================
🎉 สรุปการวิเคราะห์ Multi-Model Architecture
================================================================================
⏰ เวลาเริ่มต้น: 2025-09-14 15:56:13
⏰ เวลาสิ้นสุด: 2025-09-14 16:09:25
⏱️ เวลาที่ใช้ทั้งหมด: 792.1 วินาที (13.2 นาที)

🚀 ประสิทธิภาพการทำงาน:
   📈 ประมวลผลได้: 9 ไฟล์/ชั่วโมง
================================================================================
💾 บันทึกผลการทดสอบลงใน 'LightGBM/Multi_Time\time_test.txt' เรียบร้อยแล้ว
